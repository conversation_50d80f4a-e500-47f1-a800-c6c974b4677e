numpy>=1.20.0
pandas>=1.3.0
scipy>=1.7.0
matplotlib>=3.4.0
seaborn>=0.11.0
h5py>=3.6.0
pyyaml>=6.0
statsmodels>=0.13.0
scikit-learn>=1.0.0
pyDOE2>=1.3.0
tqdm>=4.62.0
click>=8.0.0
joblib>=1.1.0

[all]
plotly>=5.3.0
kaleido>=0.2.1
paramiko>=2.10.1
dask>=2022.1.0
distributed>=2022.1.0
torch>=1.10.0
tensorboard>=2.8.0
openpyxl>=3.0.9
xlrd>=2.0.1

[calibration]
torch>=1.10.0
tensorboard>=2.8.0

[cluster]
paramiko>=2.10.1
dask>=2022.1.0
distributed>=2022.1.0

[dev]
pytest>=6.2.5
pytest-cov>=2.12.1
pytest-xdist>=2.5.0
flake8>=4.0.1
black>=22.1.0
mypy>=0.931
bandit>=1.7.0
pre-commit>=2.15.0

[docs]
sphinx>=4.4.0
sphinx-rtd-theme>=1.0.0
nbsphinx>=0.8.8
ipython>=8.0.0

[visualization]
plotly>=5.3.0
kaleido>=0.2.1
