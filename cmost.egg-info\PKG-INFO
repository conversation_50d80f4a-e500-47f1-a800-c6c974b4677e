Metadata-Version: 2.4
Name: cmost
Version: 2.0.0
Summary: Colorectal Microsimulation Outcomes Screening Tool
Home-page: https://github.com/misselwitz/cmost
Author: <PERSON><PERSON>
Author-email: <PERSON> <<EMAIL>>
License: GPL-3.0
Project-URL: Homepage, https://github.com/misselwitz/cmost
Project-URL: Documentation, https://cmost.readthedocs.io
Project-URL: Repository, https://github.com/misselwitz/cmost
Project-URL: Bug Tracker, https://github.com/misselwitz/cmost/issues
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Healthcare Industry
Classifier: License :: OSI Approved :: GNU General Public License v3 (GPLv3)
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Scientific/Engineering :: Medical Science Apps.
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: numpy>=1.20.0
Requires-Dist: pandas>=1.3.0
Requires-Dist: scipy>=1.7.0
Requires-Dist: matplotlib>=3.4.0
Requires-Dist: seaborn>=0.11.0
Requires-Dist: h5py>=3.6.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: statsmodels>=0.13.0
Requires-Dist: scikit-learn>=1.0.0
Requires-Dist: pyDOE2>=1.3.0
Requires-Dist: tqdm>=4.62.0
Requires-Dist: click>=8.0.0
Requires-Dist: joblib>=1.1.0
Provides-Extra: visualization
Requires-Dist: plotly>=5.3.0; extra == "visualization"
Requires-Dist: kaleido>=0.2.1; extra == "visualization"
Provides-Extra: cluster
Requires-Dist: paramiko>=2.10.1; extra == "cluster"
Requires-Dist: dask>=2022.1.0; extra == "cluster"
Requires-Dist: distributed>=2022.1.0; extra == "cluster"
Provides-Extra: calibration
Requires-Dist: torch>=1.10.0; extra == "calibration"
Requires-Dist: tensorboard>=2.8.0; extra == "calibration"
Provides-Extra: dev
Requires-Dist: pytest>=6.2.5; extra == "dev"
Requires-Dist: pytest-cov>=2.12.1; extra == "dev"
Requires-Dist: pytest-xdist>=2.5.0; extra == "dev"
Requires-Dist: flake8>=4.0.1; extra == "dev"
Requires-Dist: black>=22.1.0; extra == "dev"
Requires-Dist: mypy>=0.931; extra == "dev"
Requires-Dist: bandit>=1.7.0; extra == "dev"
Requires-Dist: pre-commit>=2.15.0; extra == "dev"
Provides-Extra: docs
Requires-Dist: sphinx>=4.4.0; extra == "docs"
Requires-Dist: sphinx-rtd-theme>=1.0.0; extra == "docs"
Requires-Dist: nbsphinx>=0.8.8; extra == "docs"
Requires-Dist: ipython>=8.0.0; extra == "docs"
Provides-Extra: all
Requires-Dist: plotly>=5.3.0; extra == "all"
Requires-Dist: kaleido>=0.2.1; extra == "all"
Requires-Dist: paramiko>=2.10.1; extra == "all"
Requires-Dist: dask>=2022.1.0; extra == "all"
Requires-Dist: distributed>=2022.1.0; extra == "all"
Requires-Dist: torch>=1.10.0; extra == "all"
Requires-Dist: tensorboard>=2.8.0; extra == "all"
Requires-Dist: openpyxl>=3.0.9; extra == "all"
Requires-Dist: xlrd>=2.0.1; extra == "all"
Dynamic: home-page
Dynamic: requires-python

# CMOST v2.0

**Colorectal Microsimulation Outcomes Screening Tool**

[![License: GPL v3](https://img.shields.io/badge/License-GPLv3-blue.svg)](https://www.gnu.org/licenses/gpl-3.0)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Documentation Status](https://readthedocs.org/projects/cmost/badge/?version=latest)](https://cmost.readthedocs.io/en/latest/?badge=latest)

## Overview

CMOST (Colorectal Microsimulation Outcomes Screening Tool) is a comprehensive microsimulation framework for modeling colorectal cancer progression, screening strategies, and health economic outcomes. Originally developed by Benjamin Misselwitz and Meher Prakash (2012-2016) in MATLAB, CMOST v4.0 is a complete Python reimplementation with enhanced features, improved performance, and modern software engineering practices developed by Minmin Zhu and Lan Zhang.

The tool simulates the natural history of colorectal polyps and cancer at the individual patient level, allowing researchers and healthcare policy makers to evaluate various screening strategies, estimate cost-effectiveness, and optimize public health interventions.

## Key Features

- **Natural History Modeling**: Detailed simulation of adenoma-carcinoma sequence and serreted-carcinoma sequence
- **Screening Strategies**: Evaluate various screening modalities (colonoscopy, FIT, etc.)
- **Health Economics**: Calculate costs, QALYs, and cost-effectiveness metrics
- **Population Modeling**: Simulate diverse populations with different risk profiles
- **Calibration Tools**: Auto-calibration including deep neural network against epidemiological data
- **Visualization**: Comprehensive result visualization and reporting
- **Cluster Computing**: Distribute simulations across computing clusters
- **User Interface**: Both GUI and command-line interfaces

## Installation

### Using pip

```bash
# Basic installation
pip install cmost

# With visualization extras
pip install cmost[visualization]

# With all extras (visualization, cluster computing, calibration, etc.)
pip install cmost[all]
```

### From source

```bash
git clone https://github.com/zhuminmin1982/CMOST_python.git
cd cmost
pip install -e .
```

## Quick Start

### Unified Entry Point (New!)

CMOST now provides a unified entry point `main.py` for easy access to all features:

```bash
# Show help and available options
python main.py --help

# Launch graphical user interface
python main.py --gui

# Launch command line interface
python main.py --cli

# Run interactive Python environment
python main.py --interactive

# Run quick demonstration
python main.py --demo

# Show version information
python main.py --version
```

### Command Line Interface

```bash
# Run a basic simulation
cmost simulate --patients 10000 --output results.json

# Run a screening strategy comparison
cmost compare --strategies colonoscopy,fit,sigmoidoscopy --output comparison.json

# Calibrate model parameters
cmost calibrate --target-data seer_data.csv --output calibrated_params.json

# Generate Excel configuration template (New!)
cmost config --template excel --output my_config.xlsx
```

### Excel Configuration Support (New!)

CMOST now supports Excel-based configuration files for easier parameter management:

```python
from cmost.config.settings import Settings
from cmost.config.excel_templates import create_excel_template

# Generate Excel configuration template
create_excel_template('basic', 'my_config.xlsx')

# Load configuration from Excel file
settings = Settings()
settings.load_settings('my_config.xlsx')

# Save configuration to Excel file
settings.save_settings('output_config.xlsx')
```

Supported configuration formats:

- **JSON** (`.json`) - Standard JSON format
- **YAML** (`.yaml`, `.yml`) - Human-readable YAML format
- **MATLAB** (`.mat`) - MATLAB data format
- **Excel** (`.xlsx`, `.xls`) - Excel spreadsheet format (New!)

### Python API

```python
from cmost.core.simulation import Simulation
from cmost.config.settings import Settings
from cmost.utils.visualization import plot_results

# Create simulation settings
settings = Settings()
settings.set('Number_patients', 1000000)
settings.set('ModelParameters.male_proportion', 0.5)

# Set up and run simulation
sim = Simulation(settings)
results = sim.run(years=50)

# Analyze results
summary = sim.get_summary_statistics()
print(f"Total patients: {summary['total_patients']}")
print(f"Cancer incidence: {summary.get('cancer_incidence', 'N/A')}")
print(f"Cancer mortality: {summary.get('cancer_mortality', 'N/A')}")

# Visualize results
plot_results(results)
```

## Documentation

Comprehensive documentation is available at [https://cmost.readthedocs.io/](https://cmost.readthedocs.io/)

- [User Guide](https://cmost.readthedocs.io/en/latest/user_guide.html)
- [API Reference](https://cmost.readthedocs.io/en/latest/api.html)
- [Examples](https://cmost.readthedocs.io/en/latest/examples.html)
- [Model Description](https://cmost.readthedocs.io/en/latest/model.html)

## Project Structure (待修改)

```
cmost/
├── core/                    # Core simulation logic
│   ├── simulation.py        # Simulation engine
│   ├── progression.py       # Disease progression model
│   └── population.py        # Population model
├── models/                  # Data models
│   ├── patient.py           # Patient model
│   ├── polyp.py             # Polyp model
│   └── cancer.py            # Cancer model
├── utils/                   # Utility functions
│   ├── statistics.py        # Statistical tools
│   ├── file_io.py           # File I/O
│   └── visualization.py     # Visualization tools
├── calibration/             # Calibration
│   ├── autocalibration.py   # Auto-calibration module
│   └── benchmark.py         # Benchmark values
├── config/                  # Configuration management
│   ├── settings.py          # Settings management
│   └── defaults.py          # Default parameters
├── ui/                      # User interface
│   ├── main_window.py       # Main window
│   ├── simulation_panel.py  # Simulation control panel
│   └── results_view.py      # Results display
└── cluster/                 # Cluster computing
    ├── job_manager.py       # Job management
    └── result_collector.py  # Result collection
```

## Citing CMOST

If you use CMOST in your research, please cite:

```
Zhu MM, Zhang L.********
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the GNU General Public License v3.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Original MATLAB implementation by Benjamin Misselwitz and Meher Prakash
- GBD Program for providing epidemiological data
- All contributors and testers who have helped improve CMOST
