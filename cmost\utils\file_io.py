"""
File input/output utilities for CMOST.
"""
from typing import Dict, <PERSON>, Optional, Tuple, Any, Union
import json
import csv
import pickle
import os
import pandas as pd
import numpy as np
from datetime import datetime

def save_json(data: Dict[str, Any], filename: str, indent: int = 2) -> None:
    """Save data to JSON file.
    
    Args:
        data: Dictionary to save
        filename: Path to save file
        indent: JSON indentation level
    """
    with open(filename, 'w') as f:
        json.dump(data, f, indent=indent, default=_json_serializer)
    
    print(f"Data saved to {filename}")

def load_json(filename: str) -> Dict[str, Any]:
    """Load data from JSON file.
    
    Args:
        filename: Path to JSON file
        
    Returns:
        Dictionary containing loaded data
    """
    with open(filename, 'r') as f:
        data = json.load(f)
    
    return data

def save_csv(data: pd.DataFrame, filename: str, index: bool = False) -> None:
    """Save DataFrame to CSV file.
    
    Args:
        data: DataFrame to save
        filename: Path to save file
        index: Whether to include index in CSV
    """
    data.to_csv(filename, index=index)
    print(f"Data saved to {filename}")

def load_csv(filename: str) -> pd.DataFrame:
    """Load data from CSV file.
    
    Args:
        filename: Path to CSV file
        
    Returns:
        DataFrame containing loaded data
    """
    return pd.read_csv(filename)

def save_pickle(data: Any, filename: str) -> None:
    """Save data to pickle file.
    
    Args:
        data: Object to save
        filename: Path to save file
    """
    with open(filename, 'wb') as f:
        pickle.dump(data, f)
    
    print(f"Data saved to {filename}")

def load_pickle(filename: str) -> Any:
    """Load data from pickle file.
    
    Args:
        filename: Path to pickle file
        
    Returns:
        Object containing loaded data
    """
    with open(filename, 'rb') as f:
        data = pickle.load(f)
    
    return data

def save_simulation_results(results: Dict[str, Any], filename: str, format: str = 'json') -> None:
    """Save simulation results to file.
    
    Args:
        results: Dictionary of simulation results
        filename: Path to save file
        format: File format ('json', 'csv', or 'pickle')
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(filename)), exist_ok=True)
    
    # Save in specified format
    if format.lower() == 'json':
        save_json(results, filename)
    elif format.lower() == 'csv':
        # Convert to DataFrame for CSV
        if isinstance(results, dict):
            # Flatten nested dictionary
            flat_dict = _flatten_dict(results)
            df = pd.DataFrame([flat_dict])
            save_csv(df, filename)
        else:
            print("Error: Results must be a dictionary to save as CSV")
    elif format.lower() == 'pickle':
        save_pickle(results, filename)
    else:
        print(f"Error: Unsupported format '{format}'")

def load_simulation_results(filename: str) -> Dict[str, Any]:
    """Load simulation results from file.
    
    Args:
        filename: Path to file
        
    Returns:
        Dictionary containing simulation results
    """
    # Determine file format from extension
    _, ext = os.path.splitext(filename)
    
    if ext.lower() == '.json':
        return load_json(filename)
    elif ext.lower() == '.csv':
        df = load_csv(filename)
        # Convert DataFrame to dictionary
        return df.to_dict(orient='records')[0]
    elif ext.lower() == '.pkl' or ext.lower() == '.pickle':
        return load_pickle(filename)
    else:
        print(f"Error: Unsupported file extension '{ext}'")
        return {}

def _json_serializer(obj: Any) -> Any:
    """Custom JSON serializer for objects not serializable by default."""
    if isinstance(obj, (np.integer, np.int64)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif hasattr(obj, 'to_dict'):
        return obj.to_dict()
    else:
        return str(obj)

def _flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '_') -> Dict[str, Any]:
    """Flatten a nested dictionary."""
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(_flatten_dict(v, new_key, sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

def export_for_matlab(data: Dict[str, Any], filename: str) -> None:
    """Export data in a format readable by MATLAB.
    
    Args:
        data: Dictionary of data to export
        filename: Path to save file
    """
    # Convert nested dictionaries to structured arrays
    matlab_data = {}
    
    for key, value in data.items():
        if isinstance(value, dict):
            # Convert to structured array
            fields = list(value.keys())
            values = list(value.values())
            
            # Create structured array
            dtype = [(field, np.float64 if isinstance(val, (int, float)) else object) 
                    for field, val in zip(fields, values)]
            
            struct_array = np.zeros(1, dtype=dtype)
            for field, val in zip(fields, values):
                struct_array[field] = val
            
            matlab_data[key] = struct_array
        else:
            matlab_data[key] = value
    
    # Save as .mat file
    from scipy.io import savemat
    savemat(filename, matlab_data)
    print(f"Data exported to MATLAB format: {filename}")

def import_from_matlab(filename: str) -> Dict[str, Any]:
    """Import data from MATLAB .mat file.
    
    Args:
        filename: Path to .mat file
        
    Returns:
        Dictionary containing imported data
    """
    from scipy.io import loadmat
    
    # Load .mat file
    mat_data = loadmat(filename)
    
    # Convert to Python dictionary
    python_data = {}
    
    for key, value in mat_data.items():
        # Skip MATLAB's metadata
        if key.startswith('__'):
            continue
        
        # Convert structured arrays to dictionaries
        if isinstance(value, np.ndarray) and value.dtype.names is not None:
            # Structured array
            struct_dict = {}
            for name in value.dtype.names:
                struct_dict[name] = value[name][0]
            python_data[key] = struct_dict
        else:
            python_data[key] = value
    
    return python_data


def save_results(results: Dict[str, Any], filename: str, format: str = 'json') -> None:
    """Save simulation results to file.

    Args:
        results: Dictionary containing simulation results
        filename: Output filename
        format: Output format ('json', 'pickle', 'excel')
    """
    save_simulation_results(results, filename, format)


def load_results(filename: str) -> Dict[str, Any]:
    """Load simulation results from file.

    Args:
        filename: Input filename

    Returns:
        Dictionary containing simulation results
    """
    return load_simulation_results(filename)


def export_to_excel(data: Dict[str, Any], filename: str) -> None:
    """Export data to Excel file with multiple sheets.

    Args:
        data: Dictionary containing data to export
        filename: Output Excel filename
    """
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # Export summary statistics
        if 'summary' in data:
            summary_df = pd.DataFrame([data['summary']])
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

        # Export patient data
        if 'patients' in data and data['patients']:
            patients_df = pd.DataFrame(data['patients'])
            patients_df.to_excel(writer, sheet_name='Patients', index=False)

        # Export settings
        if 'settings' in data:
            settings_df = pd.DataFrame([data['settings']])
            settings_df.to_excel(writer, sheet_name='Settings', index=False)


def import_from_excel(filename: str) -> Dict[str, Any]:
    """Import data from Excel file.

    Args:
        filename: Input Excel filename

    Returns:
        Dictionary containing imported data
    """
    data = {}

    try:
        # Read all sheets
        excel_file = pd.ExcelFile(filename)

        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(filename, sheet_name=sheet_name)

            if sheet_name.lower() == 'summary':
                data['summary'] = df.to_dict('records')[0] if not df.empty else {}
            elif sheet_name.lower() == 'patients':
                data['patients'] = df.to_dict('records')
            elif sheet_name.lower() == 'settings':
                data['settings'] = df.to_dict('records')[0] if not df.empty else {}
            else:
                data[sheet_name] = df.to_dict('records')

    except Exception as e:
        raise ValueError(f"Error importing from Excel: {str(e)}")

    return data


def save_patient_data(patients: List, filename: str, format: str = 'csv') -> None:
    """Save patient data to file.

    Args:
        patients: List of patient objects
        filename: Output filename
        format: Output format ('csv', 'json', 'excel')
    """
    # Convert patients to DataFrame
    patient_data = []
    for patient in patients:
        if hasattr(patient, 'to_dict'):
            patient_data.append(patient.to_dict())
        else:
            # Fallback for simple patient objects
            patient_data.append({
                'id': getattr(patient, 'id', None),
                'age': getattr(patient, 'age', None),
                'gender': getattr(patient, 'gender', None),
                'death_year': getattr(patient, 'death_year', None),
                'death_cause': getattr(patient, 'death_cause', None)
            })

    df = pd.DataFrame(patient_data)

    if format.lower() == 'csv':
        df.to_csv(filename, index=False)
    elif format.lower() == 'json':
        df.to_json(filename, orient='records', indent=2)
    elif format.lower() == 'excel':
        df.to_excel(filename, index=False)
    else:
        raise ValueError(f"Unsupported format: {format}")


def load_patient_data(filename: str) -> List[Dict[str, Any]]:
    """Load patient data from file.

    Args:
        filename: Input filename

    Returns:
        List of patient dictionaries
    """
    file_ext = os.path.splitext(filename)[1].lower()

    if file_ext == '.csv':
        df = pd.read_csv(filename)
    elif file_ext == '.json':
        df = pd.read_json(filename)
    elif file_ext in ['.xlsx', '.xls']:
        df = pd.read_excel(filename)
    else:
        raise ValueError(f"Unsupported file format: {file_ext}")

    return df.to_dict('records')


def load_lifetable_from_excel(file_path: str = 'lifetable.xlsx') -> Dict[str, Dict[int, float]]:
    """
    从Excel文件加载生命表数据

    Args:
        file_path: Excel文件路径，默认为'lifetable.xlsx'

    Returns:
        包含男性和女性死亡率数据的字典，格式为:
        {
            'M': {age: mortality_rate, ...},  # 男性死亡率
            'F': {age: mortality_rate, ...}   # 女性死亡率
        }

    Raises:
        FileNotFoundError: 如果文件不存在
        ValueError: 如果文件格式不正确
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Life table file not found: {file_path}")

    try:
        # 读取Excel文件的Sheet1
        df = pd.read_excel(file_path, sheet_name='Sheet1')

        # 验证必需的列是否存在
        required_columns = ['age', 'Male', 'Female']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns in life table: {missing_columns}")

        # 转换为项目需要的格式
        mortality_tables = {
            'M': {},  # 男性
            'F': {}   # 女性
        }

        # 填充数据
        for _, row in df.iterrows():
            age = int(row['age'])
            mortality_tables['M'][age] = float(row['Male'])
            mortality_tables['F'][age] = float(row['Female'])

        print(f"✓ Successfully loaded life table from {file_path}")
        print(f"  Age range: {min(mortality_tables['M'].keys())} - {max(mortality_tables['M'].keys())}")
        print(f"  Total ages: {len(mortality_tables['M'])}")

        return mortality_tables

    except Exception as e:
        raise ValueError(f"Error loading life table from {file_path}: {str(e)}")


def load_lifetable_as_list(file_path: str = 'lifetable.xlsx') -> Dict[str, List[float]]:
    """
    从Excel文件加载生命表数据并转换为列表格式

    某些代码可能期望mortality_tables是列表格式而不是字典格式

    Args:
        file_path: Excel文件路径，默认为'lifetable.xlsx'

    Returns:
        包含男性和女性死亡率数据的字典，格式为:
        {
            'M': [rate_age_0, rate_age_1, ...],  # 男性死亡率列表
            'F': [rate_age_0, rate_age_1, ...]   # 女性死亡率列表
        }
    """
    # 先加载为字典格式
    mortality_dict = load_lifetable_from_excel(file_path)

    # 转换为列表格式
    max_age = max(max(mortality_dict['M'].keys()), max(mortality_dict['F'].keys()))

    mortality_lists = {
        'M': [0.0] * (max_age + 1),
        'F': [0.0] * (max_age + 1)
    }

    # 填充列表
    for age, rate in mortality_dict['M'].items():
        mortality_lists['M'][age] = rate

    for age, rate in mortality_dict['F'].items():
        mortality_lists['F'][age] = rate

    print(f"✓ Converted life table to list format (length: {len(mortality_lists['M'])})")

    return mortality_lists


def validate_lifetable_file(file_path: str = 'lifetable.xlsx') -> Dict[str, Any]:
    """
    验证生命表文件的格式和数据完整性

    Args:
        file_path: Excel文件路径

    Returns:
        验证结果字典，包含:
        - valid: bool, 是否有效
        - errors: List[str], 错误信息列表
        - warnings: List[str], 警告信息列表
        - summary: Dict, 数据摘要
    """
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'summary': {}
    }

    # 检查文件是否存在
    if not os.path.exists(file_path):
        result['valid'] = False
        result['errors'].append(f"File not found: {file_path}")
        return result

    try:
        # 读取Excel文件
        excel_data = pd.read_excel(file_path, sheet_name=None)

        # 检查是否有Sheet1
        if 'Sheet1' not in excel_data:
            result['valid'] = False
            result['errors'].append("Missing 'Sheet1' in Excel file")
            return result

        df = excel_data['Sheet1']

        # 检查必需的列
        required_columns = ['age', 'Male', 'Female']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            result['valid'] = False
            result['errors'].append(f"Missing required columns: {missing_columns}")

        # 检查数据完整性
        if df.empty:
            result['valid'] = False
            result['errors'].append("Life table data is empty")
        else:
            # 检查年龄范围
            age_min = df['age'].min()
            age_max = df['age'].max()
            age_count = len(df)

            result['summary'] = {
                'age_range': f"{age_min} - {age_max}",
                'total_ages': age_count,
                'columns': list(df.columns)
            }

            # 检查是否有缺失值
            missing_values = df.isnull().sum()
            if missing_values.any():
                result['warnings'].append(f"Missing values found: {missing_values.to_dict()}")

            # 检查死亡率范围是否合理
            male_rates = df['Male']
            female_rates = df['Female']

            if (male_rates < 0).any() or (female_rates < 0).any():
                result['errors'].append("Negative mortality rates found")
                result['valid'] = False

            if (male_rates > 1).any() or (female_rates > 1).any():
                result['warnings'].append("Mortality rates > 1.0 found (may be valid for very old ages)")

            # 检查年龄是否连续
            expected_ages = list(range(int(age_min), int(age_max) + 1))
            actual_ages = sorted(df['age'].astype(int).tolist())
            if actual_ages != expected_ages:
                missing_ages = set(expected_ages) - set(actual_ages)
                if missing_ages:
                    result['warnings'].append(f"Missing ages: {sorted(missing_ages)}")

    except Exception as e:
        result['valid'] = False
        result['errors'].append(f"Error reading file: {str(e)}")

    return result


def save_lifetable_to_excel(mortality_tables: Dict[str, Union[Dict[int, float], List[float]]],
                           file_path: str = 'lifetable_output.xlsx') -> None:
    """
    将生命表数据保存为Excel文件

    Args:
        mortality_tables: 生命表数据，可以是字典格式或列表格式
        file_path: 输出Excel文件路径
    """
    try:
        # 转换数据格式
        if isinstance(mortality_tables['M'], dict):
            # 字典格式
            ages = sorted(mortality_tables['M'].keys())
            data = {
                'age': ages,
                'Male': [mortality_tables['M'][age] for age in ages],
                'Female': [mortality_tables['F'][age] for age in ages]
            }
        else:
            # 列表格式
            ages = list(range(len(mortality_tables['M'])))
            data = {
                'age': ages,
                'Male': mortality_tables['M'],
                'Female': mortality_tables['F']
            }

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 保存到Excel
        df.to_excel(file_path, sheet_name='Sheet1', index=False)

        print(f"✓ Life table saved to {file_path}")
        print(f"  Age range: {df['age'].min()} - {df['age'].max()}")
        print(f"  Total ages: {len(df)}")

    except Exception as e:
        raise ValueError(f"Error saving life table to {file_path}: {str(e)}")