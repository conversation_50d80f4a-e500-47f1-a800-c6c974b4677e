#!/usr/bin/env python3
"""
改进的CMOST增强UI启动脚本 - 确保窗口可见
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """主函数 - 启动增强UI"""
    print("=" * 60)
    print("CMOST 增强UI 启动器")
    print("=" * 60)
    
    try:
        print("1. 导入必要模块...")
        from cmost.ui.enhanced_main_window import EnhancedMainWindow
        print("✓ 模块导入成功")
        
        print("2. 创建主窗口...")
        root = tk.Tk()
        
        # 设置窗口属性
        root.title("CMOST - 结直肠癌筛查仿真工具")
        root.geometry("1200x800")
        
        # 确保窗口在屏幕中央
        print("3. 设置窗口位置...")
        root.update_idletasks()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width // 2) - (1200 // 2)
        y = (screen_height // 2) - (800 // 2)
        
        # 确保窗口不会超出屏幕边界
        x = max(0, min(x, screen_width - 1200))
        y = max(0, min(y, screen_height - 800))
        
        root.geometry(f"1200x800+{x}+{y}")
        print(f"✓ 窗口位置设置为: {x}, {y}")
        
        # 设置窗口属性确保可见
        root.minsize(1000, 600)
        root.lift()  # 提升窗口到前台
        root.focus_force()  # 强制获取焦点
        root.attributes('-topmost', True)  # 临时置顶
        
        print("4. 创建应用程序...")
        app = EnhancedMainWindow(root)
        print("✓ 应用程序创建成功")
        
        # 显示启动信息
        def show_startup_info():
            """显示启动信息"""
            root.after(1000, lambda: root.attributes('-topmost', False))  # 1秒后取消置顶
            
            startup_msg = """
CMOST Enhanced UI 已成功启动！

功能特性:
• 完整的工作流程界面
• 文件选择和基本配置  
• 模型调参功能
• 筛查策略配置
• 输出配置管理
• 模拟执行和监控

使用说明:
1. 按照界面上方的步骤指示进行操作
2. 每个步骤完成后点击"下一步"
3. 可以随时返回上一步修改配置
4. 在最后一步执行仿真并查看结果

如果窗口没有显示，请检查任务栏或按Alt+Tab切换窗口。
            """
            
            print(startup_msg)
        
        # 延迟显示启动信息
        root.after(500, show_startup_info)
        
        print("5. 启动主循环...")
        print("✅ GUI窗口已启动！")
        print("   如果看不到窗口，请检查:")
        print("   - 任务栏是否有CMOST图标")
        print("   - 按Alt+Tab查看所有窗口")
        print("   - 窗口可能被其他程序遮挡")
        print("\n按Ctrl+C可以强制退出程序")
        
        # 启动主循环
        root.mainloop()
        
        print("✅ 程序正常退出")
        
    except KeyboardInterrupt:
        print("\n⚠ 用户中断程序")
        try:
            root.destroy()
        except:
            pass
        
    except Exception as e:
        error_msg = f"启动失败: {str(e)}"
        print(f"❌ {error_msg}")
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("CMOST启动错误", error_msg)
            root.destroy()
        except:
            pass
        
        import traceback
        traceback.print_exc()
        sys.exit(1)

def check_display():
    """检查显示环境"""
    print("检查显示环境...")
    
    try:
        # 测试基本的tkinter功能
        test_root = tk.Tk()
        test_root.withdraw()  # 隐藏测试窗口
        
        screen_width = test_root.winfo_screenwidth()
        screen_height = test_root.winfo_screenheight()
        
        print(f"✓ 屏幕分辨率: {screen_width} x {screen_height}")
        
        test_root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 显示环境检查失败: {e}")
        print("可能的原因:")
        print("- 没有图形界面环境")
        print("- X11转发未配置 (如果使用SSH)")
        print("- 显示驱动问题")
        return False

if __name__ == "__main__":
    print("CMOST Enhanced UI Launcher")
    print("=" * 60)
    
    # 检查显示环境
    if not check_display():
        print("❌ 无法启动图形界面")
        sys.exit(1)
    
    # 启动主程序
    main()
