# CMOST Life Table 集成总结

## 概述

本文档总结了在CMOST项目中添加专门的life table加载功能的完整实现。我们成功地在 `cmost/utils/file_io.py` 中添加了专门的函数来读取和处理 `lifetable.xlsx` 文件，并将其转换为项目需要的格式。

## 🔍 发现的问题

### 1. Life Table文件位置和格式
- **文件位置**: `lifetable.xlsx` (项目根目录)
- **文件格式**: Excel文件，包含3个工作表，主要数据在Sheet1
- **数据结构**: 
  - 列: `['age', 'Male', 'Female']`
  - 年龄范围: 0-150岁 (共151行)
  - 数据内容: 每个年龄对应的年死亡率

### 2. 项目中Life Table的使用位置
- `cmost/core/simulation.py` - 主仿真引擎
- `cmost/core/population.py` - 人口生成模块  
- `cmost/core/birth_cohort.py` - 出生队列仿真
- `cmost/models/patient.py` - 患者模型

### 3. 缺失的功能
- **没有专门的life table加载器**
- 只有硬编码的示例数据 (在`fix_simulation_settings.py`中)
- 缺乏数据验证和错误处理机制

## ✅ 实现的解决方案

### 1. 新增的Life Table加载函数

在 `cmost/utils/file_io.py` 中添加了以下函数:

#### `load_lifetable_from_excel(file_path='lifetable.xlsx')`
- **功能**: 从Excel文件加载生命表数据
- **返回格式**: `{'M': {age: rate, ...}, 'F': {age: rate, ...}}`
- **特点**: 
  - 自动验证文件格式
  - 提供详细的加载信息
  - 完整的错误处理

#### `load_lifetable_as_list(file_path='lifetable.xlsx')`
- **功能**: 加载为列表格式 (适配某些代码期望的格式)
- **返回格式**: `{'M': [rate_0, rate_1, ...], 'F': [rate_0, rate_1, ...]}`
- **用途**: 兼容期望列表格式的代码

#### `validate_lifetable_file(file_path='lifetable.xlsx')`
- **功能**: 验证生命表文件的格式和数据完整性
- **返回**: 详细的验证结果，包括错误、警告和数据摘要
- **检查项目**:
  - 文件存在性
  - 必需列的存在
  - 数据完整性
  - 死亡率范围合理性
  - 年龄连续性

#### `save_lifetable_to_excel(mortality_tables, file_path)`
- **功能**: 将生命表数据保存为Excel文件
- **支持**: 字典和列表两种输入格式
- **用途**: 数据导出和备份

### 2. 更新的模块导出

更新了 `cmost/utils/__init__.py`，将新函数添加到公共API中:
```python
from .file_io import (
    # ... 现有函数 ...
    load_lifetable_from_excel,
    load_lifetable_as_list,
    validate_lifetable_file,
    save_lifetable_to_excel
)
```

## 📊 数据格式对比

### 原始Excel格式 (lifetable.xlsx)
```
age | Male     | Female
----|----------|----------
0   | 0.002193 | 0.001838
1   | 0.001415 | 0.001182
... | ...      | ...
150 | 0.180022 | 0.153245
```

### 转换后的项目格式
```python
mortality_tables = {
    'M': {
        0: 0.002193,
        1: 0.001415,
        # ...
        150: 0.180022
    },
    'F': {
        0: 0.001838,
        1: 0.001182,
        # ...
        150: 0.153245
    }
}
```

## 🧪 测试验证

### 1. 功能测试 (`test_lifetable_loader.py`)
- ✅ 文件验证测试
- ✅ 数据加载测试 (字典和列表格式)
- ✅ 数据保存测试
- ✅ 集成测试

### 2. 集成演示 (`integrate_lifetable_demo.py`)
- ✅ 与Settings类的集成
- ✅ 仿真使用模拟
- ✅ 与硬编码数据的比较分析

### 3. 测试结果
```
通过测试: 4/4
🎉 所有测试通过！Life table加载器已准备就绪。
```

## 📈 数据质量分析

### 硬编码数据 vs 真实Life Table数据比较

| 年龄 | 性别 | 硬编码数据 | 真实数据 | 差异 |
|------|------|------------|----------|------|
| 20   | 男性 | 0.001000   | 0.000404 | 59.6% |
| 50   | 男性 | 0.005000   | 0.004058 | 18.8% |
| 80   | 男性 | 0.050000   | 0.080891 | 61.8% |
| 20   | 女性 | 0.000800   | 0.000206 | 74.2% |
| 50   | 女性 | 0.004000   | 0.001719 | 57.0% |
| 80   | 女性 | 0.040000   | 0.056019 | 40.0% |

**结论**: 真实life table数据与硬编码数据存在显著差异，使用真实数据将提高仿真的准确性。

## 🔧 使用指南

### 1. 基本使用
```python
from cmost.utils import load_lifetable_from_excel

# 加载life table数据
mortality_tables = load_lifetable_from_excel('lifetable.xlsx')

# 在Settings中使用
settings.mortality_tables = mortality_tables
```

### 2. 在仿真中集成
```python
# 在Settings类初始化时自动加载
class Settings:
    def __init__(self):
        self._load_mortality_tables()
    
    def _load_mortality_tables(self):
        try:
            from cmost.utils import load_lifetable_from_excel
            self.mortality_tables = load_lifetable_from_excel('lifetable.xlsx')
        except Exception as e:
            # 使用默认值或抛出错误
            self.mortality_tables = self._get_default_mortality_tables()
```

### 3. 在仿真核心中使用
```python
def _set_natural_death_year(self, patient):
    mortality_table = self.settings.mortality_tables[patient.gender]
    mortality_rate = mortality_table[patient.age]
    # 使用死亡率计算自然死亡年份
```

## 🚀 下一步建议

### 1. 立即可实施
- 在 `cmost/config/settings.py` 中集成自动加载功能
- 更新现有的硬编码数据使用真实life table
- 在仿真初始化时验证life table数据

### 2. 中期改进
- 添加多个life table文件支持 (不同国家/地区)
- 实现life table数据的缓存机制
- 添加年龄插值功能 (处理缺失年龄)

### 3. 长期优化
- 支持动态life table (考虑时间变化)
- 集成外部数据源 (WHO, 国家统计局等)
- 添加life table数据的可视化功能

## 📁 新增文件清单

1. **核心功能**: `cmost/utils/file_io.py` (已更新)
2. **模块导出**: `cmost/utils/__init__.py` (已更新)
3. **测试脚本**: `test_lifetable_loader.py`
4. **集成演示**: `integrate_lifetable_demo.py`
5. **分析脚本**: `analyze_lifetable.py`
6. **说明文档**: `LIFETABLE_INTEGRATION_SUMMARY.md`

## ✨ 总结

我们成功地为CMOST项目添加了完整的life table加载和处理功能。这个实现:

- ✅ **完全兼容现有代码结构**
- ✅ **提供了灵活的数据格式支持**
- ✅ **包含完整的错误处理和验证**
- ✅ **经过全面测试验证**
- ✅ **提供了详细的使用指南**

现在CMOST项目可以使用真实的生命表数据进行更准确的仿真，这将显著提高模型的可靠性和科学性。
