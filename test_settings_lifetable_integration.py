#!/usr/bin/env python3
"""
测试Settings类的life table集成功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from cmost.config.settings import Settings


def test_settings_initialization():
    """测试Settings类初始化时的life table加载"""
    print("=" * 70)
    print("测试 Settings 类初始化时的 Life Table 加载")
    print("=" * 70)
    
    try:
        # 创建新的Settings实例
        print("📋 创建 Settings 实例...")
        settings = Settings()
        
        # 检查mortality_tables是否已加载
        if hasattr(settings, 'mortality_tables') and settings.mortality_tables:
            print("✅ mortality_tables 已成功加载")
            
            # 显示基本信息
            info = settings.get_mortality_table_info()
            print(f"📊 Life Table 信息:")
            print(f"  数据源: {info['source']}")
            print(f"  性别: {info['genders']}")
            
            for gender in info['genders']:
                age_info = info['age_ranges'][gender]
                print(f"  {gender}性年龄范围: {age_info['min_age']}-{age_info['max_age']} ({age_info['total_ages']} 个年龄)")
            
            return True
        else:
            print("❌ mortality_tables 未加载")
            return False
            
    except Exception as e:
        print(f"❌ Settings初始化失败: {e}")
        return False


def test_mortality_rate_access():
    """测试死亡率数据访问"""
    print("\n" + "=" * 70)
    print("测试死亡率数据访问")
    print("=" * 70)
    
    try:
        settings = Settings()
        
        # 测试不同年龄和性别的死亡率访问
        test_cases = [
            (25, 'M'), (45, 'F'), (65, 'M'), (85, 'F'),
            (0, 'M'), (100, 'F'), (150, 'M')
        ]
        
        print("🔍 测试死亡率访问:")
        for age, gender in test_cases:
            try:
                rate = settings.get_mortality_rate(age, gender)
                gender_name = '男性' if gender == 'M' else '女性'
                print(f"  {age:3d}岁 {gender_name}: {rate:.6f}")
            except Exception as e:
                print(f"  {age:3d}岁 {gender}: 错误 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 死亡率访问测试失败: {e}")
        return False


def test_mortality_table_validation():
    """测试mortality table验证功能"""
    print("\n" + "=" * 70)
    print("测试 Mortality Table 验证功能")
    print("=" * 70)
    
    try:
        settings = Settings()
        
        # 验证mortality tables
        validation_result = settings.validate_mortality_tables()
        
        print(f"📋 验证结果:")
        print(f"  有效性: {'✅ 有效' if validation_result['valid'] else '❌ 无效'}")
        
        if validation_result['errors']:
            print(f"  错误:")
            for error in validation_result['errors']:
                print(f"    - {error}")
        
        if validation_result['warnings']:
            print(f"  警告:")
            for warning in validation_result['warnings']:
                print(f"    - {warning}")
        
        if not validation_result['errors'] and not validation_result['warnings']:
            print(f"  ✅ 没有发现问题")
        
        return validation_result['valid']
        
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        return False


def test_mortality_table_reload():
    """测试mortality table重新加载功能"""
    print("\n" + "=" * 70)
    print("测试 Mortality Table 重新加载功能")
    print("=" * 70)
    
    try:
        settings = Settings()
        
        # 获取当前信息
        original_info = settings.get_mortality_table_info()
        print(f"📊 原始数据源: {original_info['source']}")
        
        # 尝试重新加载
        print(f"🔄 尝试重新加载 life table...")
        reload_success = settings.reload_mortality_tables()
        
        if reload_success:
            new_info = settings.get_mortality_table_info()
            print(f"✅ 重新加载成功")
            print(f"📊 新数据源: {new_info['source']}")
        else:
            print(f"⚠️  重新加载失败，但这可能是正常的（如果文件不存在）")
        
        return True
        
    except Exception as e:
        print(f"❌ 重新加载测试失败: {e}")
        return False


def test_integration_with_simulation():
    """测试与仿真系统的集成"""
    print("\n" + "=" * 70)
    print("测试与仿真系统的集成")
    print("=" * 70)
    
    try:
        settings = Settings()
        
        # 模拟仿真中的使用场景
        print("🎯 模拟仿真使用场景:")
        
        # 模拟患者数据
        patients = [
            {'id': 1, 'age': 35, 'gender': 'M'},
            {'id': 2, 'age': 58, 'gender': 'F'},
            {'id': 3, 'age': 72, 'gender': 'M'},
            {'id': 4, 'age': 89, 'gender': 'F'},
        ]
        
        for patient in patients:
            try:
                # 这是仿真中常见的使用方式
                mortality_rate = settings.mortality_tables[patient['gender']][patient['age']]
                
                # 或者使用更安全的方法
                safe_rate = settings.get_mortality_rate(patient['age'], patient['gender'])
                
                gender_name = '男性' if patient['gender'] == 'M' else '女性'
                print(f"  患者{patient['id']} ({patient['age']}岁{gender_name}): 死亡率 {mortality_rate:.6f}")
                
                # 验证两种方法结果一致
                if abs(mortality_rate - safe_rate) > 1e-10:
                    print(f"    ⚠️  两种访问方法结果不一致!")
                
            except Exception as e:
                print(f"  患者{patient['id']}: 错误 - {e}")
        
        # 测试边界情况
        print(f"\n🔍 测试边界情况:")
        
        # 测试超出范围的年龄
        try:
            rate = settings.get_mortality_rate(200, 'M')  # 超出范围
            print(f"  200岁男性: {rate:.6f} (使用最大年龄的数据)")
        except Exception as e:
            print(f"  200岁男性: 错误 - {e}")
        
        # 测试无效性别
        try:
            rate = settings.get_mortality_rate(50, 'X')  # 无效性别
            print(f"  50岁X性: {rate:.6f}")
        except Exception as e:
            print(f"  50岁X性: 错误 - {e} (这是预期的)")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def test_global_settings_instance():
    """测试全局settings实例"""
    print("\n" + "=" * 70)
    print("测试全局 Settings 实例")
    print("=" * 70)
    
    try:
        # 导入全局settings实例
        from cmost.config.settings import settings
        
        print("📋 测试全局settings实例...")
        
        # 检查mortality_tables是否可用
        if hasattr(settings, 'mortality_tables') and settings.mortality_tables:
            print("✅ 全局settings实例的mortality_tables可用")
            
            # 测试访问
            test_rate = settings.get_mortality_rate(50, 'M')
            print(f"  50岁男性死亡率: {test_rate:.6f}")
            
            # 显示信息
            info = settings.get_mortality_table_info()
            print(f"  数据源: {info['source']}")
            
        else:
            print("❌ 全局settings实例的mortality_tables不可用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 全局settings测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Settings Life Table 集成测试")
    print("=" * 70)
    
    # 运行所有测试
    tests = [
        ("Settings初始化", test_settings_initialization),
        ("死亡率访问", test_mortality_rate_access),
        ("数据验证", test_mortality_table_validation),
        ("重新加载", test_mortality_table_reload),
        ("仿真集成", test_integration_with_simulation),
        ("全局实例", test_global_settings_instance),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！Settings类life table集成成功。")
        
        print(f"\n💡 使用建议:")
        print(f"```python")
        print(f"# 方法1: 使用全局settings实例")
        print(f"from cmost.config.settings import settings")
        print(f"mortality_rate = settings.get_mortality_rate(age, gender)")
        print(f"")
        print(f"# 方法2: 创建新的Settings实例")
        print(f"from cmost.config.settings import Settings")
        print(f"my_settings = Settings()")
        print(f"mortality_rate = my_settings.mortality_tables[gender][age]")
        print(f"```")
        
    else:
        print("⚠️  部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    main()
