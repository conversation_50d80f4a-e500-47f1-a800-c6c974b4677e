#!/usr/bin/env python3
"""
演示完整的Life Table集成效果
展示Settings类自动加载life table，以及在仿真中的使用
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from cmost.config.settings import Settings
from cmost.models.patient import Patient


def demo_settings_auto_loading():
    """演示Settings类自动加载life table"""
    print("=" * 70)
    print("演示 Settings 类自动加载 Life Table")
    print("=" * 70)
    
    print("📋 创建Settings实例（应该自动加载life table）...")
    settings = Settings()
    
    # 检查life table是否已加载
    if hasattr(settings, 'mortality_tables') and settings.mortality_tables:
        print("✅ Life table已自动加载")
        
        # 显示详细信息
        info = settings.get_mortality_table_info()
        print(f"📊 Life Table信息:")
        print(f"  数据源: {info['source']}")
        print(f"  性别: {info['genders']}")
        
        for gender in info['genders']:
            age_info = info['age_ranges'][gender]
            print(f"  {gender}性: {age_info['min_age']}-{age_info['max_age']}岁 ({age_info['total_ages']}个数据点)")
        
        return settings
    else:
        print("❌ Life table未能自动加载")
        return None


def demo_mortality_rate_access(settings):
    """演示死亡率数据访问的不同方式"""
    print("\n" + "=" * 70)
    print("演示死亡率数据访问的不同方式")
    print("=" * 70)
    
    test_ages = [25, 45, 65, 85]
    test_genders = ['M', 'F']
    
    print("🔍 比较不同的访问方式:")
    print("年龄 性别  直接访问      安全方法      差异")
    print("-" * 50)
    
    for age in test_ages:
        for gender in test_genders:
            try:
                # 方式1: 直接访问 (仿真中常用)
                direct_rate = settings.mortality_tables[gender][age]
                
                # 方式2: 安全方法 (推荐)
                safe_rate = settings.get_mortality_rate(age, gender)
                
                # 计算差异
                diff = abs(direct_rate - safe_rate)
                
                gender_name = '男性' if gender == 'M' else '女性'
                print(f"{age:3d}  {gender_name}  {direct_rate:.6f}  {safe_rate:.6f}  {diff:.2e}")
                
            except Exception as e:
                print(f"{age:3d}  {gender}: 错误 - {e}")


def demo_simulation_integration(settings):
    """演示在仿真中的集成使用"""
    print("\n" + "=" * 70)
    print("演示在仿真中的集成使用")
    print("=" * 70)
    
    # 创建一些测试患者
    patients = [
        Patient(id=1, age=30, gender='M', risk_factors={}),
        Patient(id=2, age=55, gender='F', risk_factors={}),
        Patient(id=3, age=75, gender='M', risk_factors={}),
        Patient(id=4, age=90, gender='F', risk_factors={}),
    ]
    
    print("🎯 模拟仿真中的自然死亡年份设置:")
    
    for patient in patients:
        try:
            # 模拟 _set_natural_death_year 的逻辑
            current_age = patient.age
            death_age = current_age
            max_age = 150
            
            # 使用Settings的安全方法
            import random
            random.seed(42 + patient.id)  # 为了可重现的结果
            
            while death_age <= max_age:
                mortality_rate = settings.get_mortality_rate(death_age, patient.gender)
                if random.random() < mortality_rate:
                    break
                death_age += 1
            
            if death_age > max_age:
                death_age = max_age
            
            years_to_death = death_age - current_age
            gender_name = '男性' if patient.gender == 'M' else '女性'
            
            print(f"  患者{patient.id} ({current_age}岁{gender_name}): 预期{years_to_death}年后去世 (死亡年龄: {death_age})")
            
            # 设置到患者对象
            patient.set_natural_death_year(years_to_death)
            
        except Exception as e:
            print(f"  患者{patient.id}: 错误 - {e}")
    
    return patients


def demo_error_handling(settings):
    """演示错误处理和边界情况"""
    print("\n" + "=" * 70)
    print("演示错误处理和边界情况")
    print("=" * 70)
    
    test_cases = [
        (200, 'M', "超出年龄范围"),
        (-5, 'F', "负年龄"),
        (50, 'X', "无效性别"),
        (75, 'f', "小写性别"),
    ]
    
    print("🔍 测试边界情况和错误处理:")
    
    for age, gender, description in test_cases:
        try:
            rate = settings.get_mortality_rate(age, gender)
            print(f"  {description} ({age}岁, {gender}): {rate:.6f}")
        except Exception as e:
            print(f"  {description} ({age}岁, {gender}): 错误 - {e}")


def demo_validation_and_info():
    """演示验证和信息功能"""
    print("\n" + "=" * 70)
    print("演示验证和信息功能")
    print("=" * 70)
    
    settings = Settings()
    
    # 验证mortality tables
    print("📋 验证mortality tables:")
    validation = settings.validate_mortality_tables()
    
    print(f"  有效性: {'✅ 有效' if validation['valid'] else '❌ 无效'}")
    
    if validation['errors']:
        print("  错误:")
        for error in validation['errors']:
            print(f"    - {error}")
    
    if validation['warnings']:
        print("  警告:")
        for warning in validation['warnings']:
            print(f"    - {warning}")
    
    if not validation['errors'] and not validation['warnings']:
        print("  ✅ 没有发现问题")
    
    # 显示详细信息
    print(f"\n📊 详细信息:")
    info = settings.get_mortality_table_info()
    
    for key, value in info.items():
        if key == 'age_ranges':
            print(f"  {key}:")
            for gender, age_info in value.items():
                print(f"    {gender}: {age_info}")
        else:
            print(f"  {key}: {value}")


def demo_global_settings():
    """演示全局settings实例的使用"""
    print("\n" + "=" * 70)
    print("演示全局 Settings 实例的使用")
    print("=" * 70)
    
    # 导入全局settings
    from cmost.config.settings import settings as global_settings
    
    print("📋 使用全局settings实例:")
    
    # 检查是否已加载
    if hasattr(global_settings, 'mortality_tables') and global_settings.mortality_tables:
        print("✅ 全局settings的mortality_tables可用")
        
        # 测试访问
        test_rate = global_settings.get_mortality_rate(60, 'M')
        print(f"  60岁男性死亡率: {test_rate:.6f}")
        
        # 显示数据源
        info = global_settings.get_mortality_table_info()
        print(f"  数据源: {info['source']}")
        
        print(f"\n💡 在其他模块中的使用方式:")
        print(f"```python")
        print(f"from cmost.config.settings import settings")
        print(f"mortality_rate = settings.get_mortality_rate(patient.age, patient.gender)")
        print(f"```")
        
    else:
        print("❌ 全局settings的mortality_tables不可用")


def main():
    """主演示函数"""
    print("🚀 CMOST Life Table 完整集成演示")
    print("=" * 70)
    
    # 检查lifetable.xlsx是否存在
    if not os.path.exists('lifetable.xlsx'):
        print("⚠️  警告: lifetable.xlsx文件不存在")
        print("   演示将使用默认的mortality数据")
    
    # 1. 演示自动加载
    settings = demo_settings_auto_loading()
    
    if settings:
        # 2. 演示数据访问
        demo_mortality_rate_access(settings)
        
        # 3. 演示仿真集成
        patients = demo_simulation_integration(settings)
        
        # 4. 演示错误处理
        demo_error_handling(settings)
        
        # 5. 演示验证功能
        demo_validation_and_info()
        
        # 6. 演示全局实例
        demo_global_settings()
        
        print("\n" + "=" * 70)
        print("🎉 完整集成演示完成!")
        print("=" * 70)
        
        print("✅ 主要成果:")
        print("  - Settings类现在自动加载life table数据")
        print("  - 提供了安全的死亡率访问方法")
        print("  - 更新了所有仿真模块以使用新的life table")
        print("  - 添加了完整的错误处理和验证")
        print("  - 支持全局settings实例")
        
        print(f"\n💡 使用建议:")
        print(f"  1. 在仿真中使用: settings.get_mortality_rate(age, gender)")
        print(f"  2. 直接访问: settings.mortality_tables[gender][age]")
        print(f"  3. 验证数据: settings.validate_mortality_tables()")
        print(f"  4. 重新加载: settings.reload_mortality_tables()")
        
    else:
        print("\n❌ 演示失败: Settings类未能正确加载life table")


if __name__ == "__main__":
    main()
