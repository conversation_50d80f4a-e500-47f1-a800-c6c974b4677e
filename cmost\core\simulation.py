"""
Core simulation engine for CMOST.
"""
from typing import Dict, List, Optional, Tuple, Any, Callable
import numpy as np
import pandas as pd
from tqdm import tqdm
import logging
from datetime import datetime

from ..models.patient import Patient
from ..models.polyp import Polyp
from ..models.cancer import Cancer
from ..config.settings import Settings
from ..utils.statistics import calculate_statistics
from ..screening.strategy_manager import ScreeningStrategyManager
from ..screening.dynamic_scheduler import DynamicScreeningScheduler

# Performance optimization imports
from ..utils.performance import performance_monitor, profile, memory_limit
from ..utils.parallel import ParallelProcessor, ParallelConfig, parallel_map
from ..utils.memory import memory_manager, managed_object, memory_monitor
from ..utils.cache import cached, default_cache


class Simulation:
    """Main simulation engine for colorectal cancer progression."""

    def __init__(self, settings: Settings):
        """Initialize simulation with settings."""
        self.settings = settings
        self.patients: List[Patient] = []
        self.results: Dict[str, Any] = {}
        self.current_year = 0
        self.start_time = None
        self.end_time = None
        self.progression_model = self._load_progression_model()
        self.screening_manager = ScreeningStrategyManager()
        self.dynamic_scheduler = DynamicScreeningScheduler()
        self.logger = self._setup_logger()

        # Performance optimization setup
        self.enable_parallel = self._get_setting('Simulation.EnableMultiprocessing', True)
        self.num_processes = self._get_setting('Simulation.NumProcesses', 0)
        self.parallel_config = ParallelConfig(
            max_workers=self.num_processes if self.num_processes > 0 else None,
            use_processes=self.enable_parallel
        )
        self.parallel_processor = ParallelProcessor(self.parallel_config)

        # Initialize memory pools for common objects
        self._setup_memory_pools()

    def _get_setting(self, key: str, default=None):
        """Helper method to get settings with dot notation."""
        return self.settings.get(key, default)

    def _setup_memory_pools(self):
        """设置内存池以优化对象分配"""
        # 创建患者对象池
        memory_manager.create_pool(
            'patient_pool',
            lambda: Patient(id=0, age=0, gender='M'),
            reset_func=lambda p: p.reset() if hasattr(p, 'reset') else None,
            max_size=1000,
            initial_size=100
        )

        # 创建息肉对象池
        memory_manager.create_pool(
            'polyp_pool',
            lambda: Polyp(id=0, location=1, size=0.1, stage=1, patient_id=0, patient_gender='M'),
            reset_func=lambda p: p.reset() if hasattr(p, 'reset') else None,
            max_size=5000,
            initial_size=500
        )

        self.logger.info("Memory pool initialization completed")
        
    def _setup_logger(self):
        """Set up logging for the simulation."""
        logger = logging.getLogger("CMOST_Simulation")
        if not logger.handlers:
            logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
        
    def _load_progression_model(self):
        """Load the disease progression model based on settings."""
        from .progression import ProgressionModel
        return ProgressionModel(
            dwell_speed=self._get_setting('ModelParameters.dwell_speed', 1.0),
            location_factors=self._get_setting('ModelParameters.location_progression_factors', {}),
            gender_factors=self._get_setting('ModelParameters.gender_progression_factors', {})
        )
    
    @profile("population_initialization")
    def initialize_population(self) -> None:
        """Create initial patient population with performance optimization."""
        num_patients = self._get_setting('Number_patients', 10000)
        self.logger.info(f"Initializing population with {num_patients} patients")

        with memory_monitor(threshold_mb=500):
            if self.enable_parallel and num_patients > 1000:
                # 并行初始化大规模人群
                self._initialize_population_parallel(num_patients)
            else:
                # 串行初始化小规模人群
                self._initialize_population_serial(num_patients)

        self.logger.info(f"Population initialization complete: {len(self.patients)} patients")

    def _initialize_population_serial(self, num_patients: int):
        """串行初始化患者人群"""
        for i in range(num_patients):
            patient = self._create_patient(i)
            self.patients.append(patient)

    def _initialize_population_parallel(self, num_patients: int):
        """并行初始化患者人群"""
        # 创建患者ID列表
        patient_ids = list(range(num_patients))

        # 并行创建患者
        self.logger.info(f"Using {self.parallel_config.max_workers} processes for parallel patient initialization")
        patients = parallel_map(
            self._create_patient,
            patient_ids,
            max_workers=self.parallel_config.max_workers,
            use_processes=self.parallel_config.use_processes
        )

        # 过滤掉None值（如果有创建失败的患者）
        self.patients = [p for p in patients if p is not None]

    @cached(maxsize=1000, ttl=3600)  # 缓存1小时
    def _create_patient(self, patient_id: int) -> Patient:
        """创建单个患者（可缓存）"""
        try:
            gender = 'M' if np.random.random() < self._get_setting('ModelParameters.male_proportion', 0.5) else 'F'
            age = self._sample_age_distribution(gender)

            patient = Patient(
                id=patient_id,
                age=age,
                gender=gender,
                risk_factors=self._assign_risk_factors()
            )

            # Initialize with polyps based on prevalence
            self._initialize_polyps(patient)

            # Set natural death year based on life tables
            self._set_natural_death_year(patient)

            return patient

        except Exception as e:
            self.logger.error(f"Failed to create patient {patient_id}: {e}")
            return None
    
    def _sample_age_distribution(self, gender: str) -> int:
        """Sample from age distribution based on gender."""
        if hasattr(self.settings, 'age_distribution') and self.settings.age_distribution:
            # Use configured age distribution if available
            age_dist = self.settings.age_distribution.get(gender, self.settings.age_distribution.get('default', None))
            if age_dist:
                return int(age_dist.sample())
        
        # Default implementation with normal distribution
        mean_age = 50
        std_dev = 15
        return max(18, min(100, int(np.random.normal(mean_age, std_dev))))
    
    def _assign_risk_factors(self) -> Dict[str, float]:
        """Assign risk factors to a patient."""
        risk_factors = {}
        for factor, distribution in self.settings.risk_factor_distributions.items():
            risk_factors[factor] = distribution.sample()
        return risk_factors
    
    def _initialize_polyps(self, patient: Patient) -> None:
        """Initialize patient with polyps based on prevalence."""
        # Determine number of initial polyps based on age, gender, and risk
        age_key = min(patient.age // 10 * 10, max(self.settings.polyp_prevalence_by_age.keys()))
        base_prevalence = self.settings.polyp_prevalence_by_age[age_key]
        adjusted_prevalence = base_prevalence * patient.individual_risk
        
        num_polyps = np.random.poisson(adjusted_prevalence)
        
        for _ in range(num_polyps):
            location = self._sample_polyp_location()
            patient.add_polyp(location)
    
    def _sample_polyp_location(self) -> int:
        """Sample polyp location based on anatomical distribution."""
        return np.random.choice(
            range(1, 7),  # 6 colon segments
            p=self.settings.polyp_location_distribution
        )
    
    def _set_natural_death_year(self, patient: Patient) -> None:
        """Set the year when patient would die from natural causes."""
        current_age = patient.age

        # Check if mortality tables are available
        if not hasattr(self.settings, 'mortality_tables') or self.settings.mortality_tables is None:
            self.logger.warning("Mortality tables not available, using default mortality rate")
            # Use a simple default mortality calculation
            death_age = current_age + max(1, int(np.random.exponential(20)))  # Average 20 years
            patient.set_natural_death_year(self.current_year + (death_age - current_age))
            return

        mortality_table = self.settings.mortality_tables[patient.gender]

        # Determine death year using life table
        death_age = current_age
        max_age = max(mortality_table.keys()) if isinstance(mortality_table, dict) else len(mortality_table) - 1

        while death_age <= max_age:
            # Get mortality rate for current age
            if isinstance(mortality_table, dict):
                mortality_rate = mortality_table.get(death_age, mortality_table.get(max_age, 0.1))
            else:
                # Handle legacy list format
                mortality_rate = mortality_table[min(death_age, len(mortality_table) - 1)]

            if np.random.random() < mortality_rate:
                break

            death_age += 1

        # If we've reached maximum age, set death at max age
        if death_age > max_age:
            death_age = max_age

        patient.set_natural_death_year(self.current_year + (death_age - current_age))
    
    @profile("simulation_run")
    def run(self, years: int, callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Run simulation for specified number of years with performance optimization.

        Args:
            years: Number of years to simulate
            callback: Optional callback function called every 5 years with current state

        Returns:
            Dict containing simulation results
        """
        self.start_time = datetime.now()
        self.logger.info(f"Starting simulation for {years} years")

        with performance_monitor.monitor("full_simulation"):
            if not self.patients:
                self.initialize_population()

            # 定期清理内存和缓存
            gc_interval = max(1, years // 10)  # 每10%的时间清理一次

            for year in tqdm(range(years), desc="Simulating years"):
                with performance_monitor.monitor("single_year"):
                    self._simulate_year()

                # 定期内存清理
                if year % gc_interval == 0:
                    memory_manager.force_gc()
                    default_cache.l1_cache.cleanup_expired()

                if callback and year % 5 == 0:
                    callback(self.current_year, self.patients)
        
        self.end_time = datetime.now()
        duration = (self.end_time - self.start_time).total_seconds()
        self.logger.info(f"Simulation completed in {duration:.2f} seconds")
        
        self.results = calculate_statistics(self.patients, self.settings)
        return self.results
    
    def _simulate_year(self) -> None:
        """Simulate one year for all patients with performance optimization."""
        self.current_year += 1

        # Apply screening if scheduled for this year
        if self._is_screening_year():
            self._perform_screening()

        # 获取活着的患者列表
        alive_patients = [p for p in self.patients if p.is_alive(self.current_year)]

        if self.enable_parallel and len(alive_patients) > 500:
            # 并行处理患者
            self._simulate_patients_parallel(alive_patients)
        else:
            # 串行处理患者
            self._simulate_patients_serial(alive_patients)

    def _simulate_patients_serial(self, patients: List[Patient]):
        """串行处理患者年度仿真"""
        for patient in patients:
            self._simulate_patient_year(patient)

    def _simulate_patients_parallel(self, patients: List[Patient]):
        """并行处理患者年度仿真"""
        # 使用并行处理器处理患者批次
        batch_size = max(10, len(patients) // (self.parallel_config.max_workers or 4))
        patient_batches = [patients[i:i + batch_size] for i in range(0, len(patients), batch_size)]

        def process_patient_batch(batch):
            """处理患者批次"""
            for patient in batch:
                self._simulate_patient_year(patient)
            return len(batch)

        # 并行处理批次
        processed_counts = parallel_map(
            process_patient_batch,
            patient_batches,
            max_workers=self.parallel_config.max_workers,
            use_processes=False  # 使用线程而不是进程，避免序列化开销
        )

        total_processed = sum(count for count in processed_counts if count)
        self.logger.debug(f"Processed {total_processed} patients in parallel")

    def _simulate_patient_year(self, patient: Patient):
        """处理单个患者的年度仿真"""
        # Check for natural death
        if self._check_natural_death(patient):
            patient.death_year = self.current_year
            patient.death_cause = "natural"
            return

        # Generate new polyps
        self._generate_new_polyps(patient)

        # Progress existing conditions
        patient.progress(1, self.progression_model)

        # Check for cancer death
        if self._check_cancer_death(patient):
            patient.death_year = self.current_year
            patient.death_cause = "cancer"
    
    def _is_screening_year(self) -> bool:
        """Check if current year is a screening year based on settings."""
        if not hasattr(self.settings, 'screening') or not self.settings.screening:
            return False

        screening = self.settings.screening
        if not screening.get('enabled', False):
            return False

        # If using dynamic scheduling, check individual patients
        if screening.get('use_dynamic_scheduling', False):
            return True  # Always check, let dynamic scheduler decide per patient

        start_year = screening.get('start_year', 0)
        interval = screening.get('interval', 1)

        return self.current_year >= start_year and (self.current_year - start_year) % interval == 0
    
    def _perform_screening(self) -> None:
        """Perform screening on eligible patients using strategy manager."""
        if not hasattr(self.settings, 'screening') or not self.settings.screening:
            return

        screening = self.settings.screening
        strategy_name = screening.get('strategy', 'colonoscopy_only')
        participation_rate = screening.get('participation_rate', 1.0)

        self.logger.info(f"Performing {strategy_name} screening in year {self.current_year}")

        # Check if using dynamic scheduling
        use_dynamic = screening.get('use_dynamic_scheduling', False)

        for patient in self.patients:
            if not patient.is_alive(self.current_year):
                continue

            # For dynamic scheduling, check if screening is due for this patient
            if use_dynamic:
                if not self.dynamic_scheduler.is_screening_due(patient, self.current_year):
                    continue

                # Get recommended test for this patient
                recommended_test = self.dynamic_scheduler.get_recommended_test(patient, self.current_year)
                # Override strategy with personalized recommendation
                if recommended_test in ['colonoscopy', 'sigmoidoscopy', 'fit']:
                    strategy_name = f"{recommended_test}_only"

            # Check if patient participates in screening
            if np.random.random() > participation_rate:
                continue

            # Get last screening information
            last_screening = None
            if patient.screening_history:
                last_screening = patient.screening_history[-1]

            try:
                # Apply screening strategy
                result = self.screening_manager.apply_strategy(
                    strategy_name,
                    patient,
                    self.current_year,
                    last_screening
                )

                # Record screening in patient history
                patient.screening_history.append(result)

                # Process screening results
                self._process_screening_results(patient, result)

                # If using dynamic scheduling, schedule next screening
                if use_dynamic:
                    next_screening_year = self.dynamic_scheduler.schedule_next_screening(
                        patient, result, self.current_year
                    )
                    if next_screening_year:
                        self.logger.debug(f"Next screening for patient {patient.id} scheduled for year {next_screening_year}")

            except Exception as e:
                self.logger.error(f"Error applying screening strategy {strategy_name} to patient {patient.id}: {e}")
                # Fallback to simple colonoscopy
                self._perform_colonoscopy(patient)

    def _process_screening_results(self, patient: Patient, result: Dict[str, Any]) -> None:
        """Process screening results and remove detected lesions.

        Args:
            patient: Patient who was screened
            result: Screening results from strategy manager
        """
        if not result.get("eligible", True):
            return

        # Handle single test results
        if "findings" in result and result["findings"]:
            findings = result["findings"]
            self._remove_detected_lesions(patient, findings)

        # Handle sequential strategy results
        elif "primary_test" in result:
            primary_findings = result["primary_test"].get("findings", {})
            if primary_findings:
                self._remove_detected_lesions(patient, primary_findings)

            # Handle follow-up test
            if "follow_up_test" in result:
                followup_findings = result["follow_up_test"].get("findings", {})
                if followup_findings:
                    self._remove_detected_lesions(patient, followup_findings)

        # Handle parallel strategy results
        elif "tests" in result:
            for test_name, test_result in result["tests"].items():
                test_findings = test_result.get("findings", {})
                if test_findings:
                    self._remove_detected_lesions(patient, test_findings)

    def _remove_detected_lesions(self, patient: Patient, findings: Dict[str, Any]) -> None:
        """Remove detected lesions from patient.

        Args:
            patient: Patient with detected lesions
            findings: Detection findings
        """
        # Remove detected polyps
        for polyp_id in findings.get("polyps", []):
            patient.remove_polyp(polyp_id)

        # Remove detected serrated lesions
        for lesion_id in findings.get("serrated_lesions", []):
            patient.remove_serrated_lesion(lesion_id)

        # Apply cancer treatment for detected cancers
        for cancer_id in findings.get("cancers", []):
            for cancer in patient.cancers:
                if cancer.id == cancer_id:
                    self._apply_cancer_treatment(patient, cancer)
                    break

    def _perform_colonoscopy(self, patient: Patient) -> None:
        """Perform colonoscopy screening on a patient."""
        sensitivity = self.settings.screening.get('colonoscopy_sensitivity', 0.95)
        
        findings = {
            'polyp_count': 0,
            'serrated_lesion_count': 0,
            'cancer_count': 0,
            'polyps_removed': [],
            'serrated_lesions_removed': [],
            'cancers_detected': []
        }

        # Check for polyps
        for polyp in list(patient.polyps):  # Use list to allow removal during iteration
            if np.random.random() < sensitivity:
                # Polyp detected
                findings['polyp_count'] += 1
                findings['polyps_removed'].append(polyp.id)

                # Remove polyp
                patient.remove_polyp(polyp.id)

        # Check for serrated lesions (harder to detect than adenomas)
        for lesion in list(patient.serrated_lesions):
            lesion_sensitivity = lesion.get_screening_detectability('colonoscopy')
            if np.random.random() < lesion_sensitivity:
                # Serrated lesion detected
                findings['serrated_lesion_count'] += 1
                findings['serrated_lesions_removed'].append(lesion.id)

                # Remove serrated lesion
                patient.remove_serrated_lesion(lesion.id)

        # Check for cancers
        for cancer in patient.cancers:
            if np.random.random() < sensitivity:
                # Cancer detected
                findings['cancer_count'] += 1
                findings['cancers_detected'].append(cancer.id)

                # Apply treatment based on cancer stage
                self._apply_cancer_treatment(patient, cancer)
        
        # Record screening
        patient.add_screening(self.current_year, 'colonoscopy', findings)
    
    def _perform_sigmoidoscopy(self, patient: Patient) -> None:
        """Perform sigmoidoscopy screening on a patient."""
        sensitivity = self.settings.screening.get('sigmoidoscopy_sensitivity', 0.90)
        # Sigmoidoscopy only examines distal colon (locations 1-3)
        distal_locations = [1, 2, 3]
        
        findings = {
            'polyp_count': 0,
            'serrated_lesion_count': 0,
            'cancer_count': 0,
            'polyps_removed': [],
            'serrated_lesions_removed': [],
            'cancers_detected': []
        }

        # Check for polyps in distal colon
        for polyp in list(patient.polyps):
            if polyp.location in distal_locations and np.random.random() < sensitivity:
                # Polyp detected
                findings['polyp_count'] += 1
                findings['polyps_removed'].append(polyp.id)

                # Remove polyp
                patient.remove_polyp(polyp.id)

        # Check for serrated lesions in distal colon
        for lesion in list(patient.serrated_lesions):
            if lesion.location in distal_locations:
                lesion_sensitivity = lesion.get_screening_detectability('sigmoidoscopy')
                if np.random.random() < lesion_sensitivity:
                    # Serrated lesion detected
                    findings['serrated_lesion_count'] += 1
                    findings['serrated_lesions_removed'].append(lesion.id)

                    # Remove serrated lesion
                    patient.remove_serrated_lesion(lesion.id)

        # Check for cancers in distal colon
        for cancer in patient.cancers:
            if cancer.location in distal_locations and np.random.random() < sensitivity:
                # Cancer detected
                findings['cancer_count'] += 1
                findings['cancers_detected'].append(cancer.id)

                # Apply treatment
                self._apply_cancer_treatment(patient, cancer)
        
        # Record screening
        patient.add_screening(self.current_year, 'sigmoidoscopy', findings)
    
    def _perform_fobt(self, patient: Patient) -> None:
        """Perform fecal occult blood test (FOBT) screening on a patient."""
        sensitivity_polyp = self.settings.screening.get('fobt_sensitivity_polyp', 0.10)
        sensitivity_cancer = self.settings.screening.get('fobt_sensitivity_cancer', 0.70)
        
        findings = {
            'result': 'negative',
            'follow_up': None
        }
        
        # Check for advanced polyps
        has_advanced_polyps = any(p.size > 1.0 for p in patient.polyps)
        if has_advanced_polyps and np.random.random() < sensitivity_polyp:
            findings['result'] = 'positive'

        # Check for advanced serrated lesions (lower sensitivity than adenomas)
        has_advanced_serrated = any(l.is_advanced for l in patient.serrated_lesions)
        if has_advanced_serrated:
            # Use lesion-specific detectability for FIT
            for lesion in patient.serrated_lesions:
                if lesion.is_advanced:
                    lesion_sensitivity = lesion.get_screening_detectability('fit')
                    if np.random.random() < lesion_sensitivity:
                        findings['result'] = 'positive'
                        break

        # Check for cancer
        if patient.cancers and np.random.random() < sensitivity_cancer:
            findings['result'] = 'positive'
        
        # If positive, schedule follow-up colonoscopy
        if findings['result'] == 'positive':
            findings['follow_up'] = 'colonoscopy'
            # Perform follow-up colonoscopy
            self._perform_colonoscopy(patient)
        
        # Record screening
        patient.add_screening(self.current_year, 'fobt', findings)
    
    def _apply_cancer_treatment(self, patient: Patient, cancer: Cancer) -> None:
        """Apply treatment to a detected cancer."""
        treatment_type = 'surgery'
        if cancer.stage >= 3:
            treatment_type = 'surgery+chemo'
        
        # Record treatment
        treatment_details = {
            'cancer_id': cancer.id,
            'cancer_stage': cancer.stage,
            'cancer_location': cancer.location
        }
        
        patient.add_treatment(self.current_year, treatment_type, treatment_details)
        
        # Apply treatment effect (simplified model)
        # In a more detailed model, this would modify cancer progression
        if cancer.stage < 4:
            # For non-metastatic cancer, treatment has high success rate
            cancer.treatment_applied = True
    
    def _check_natural_death(self, patient: Patient) -> bool:
        """Check if patient dies from natural causes this year."""
        if patient.natural_death_year is not None and self.current_year >= patient.natural_death_year:
            return True

        # Fallback to annual probability if natural_death_year not set
        if not hasattr(self.settings, 'mortality_tables') or self.settings.mortality_tables is None:
            # Use a simple default mortality rate based on age
            default_rate = min(0.001 * (patient.age / 50) ** 2, 0.2)  # Increases with age
            return np.random.random() < default_rate

        try:
            # Use the safer method from Settings class if available
            if hasattr(self.settings, 'get_mortality_rate'):
                mortality_rate = self.settings.get_mortality_rate(patient.age, patient.gender)
            else:
                # Direct access with error handling
                mortality_table = self.settings.mortality_tables[patient.gender]
                if isinstance(mortality_table, dict):
                    mortality_rate = mortality_table.get(patient.age, 0.001)
                else:
                    # Handle legacy list format
                    mortality_rate = mortality_table[min(patient.age, len(mortality_table) - 1)]

            return np.random.random() < mortality_rate

        except (KeyError, IndexError, ValueError) as e:
            self.logger.warning(f"Error accessing mortality rate for age {patient.age}, gender {patient.gender}: {e}")
            # Use age-based default
            default_rate = min(0.001 * (patient.age / 50) ** 2, 0.2)
            return np.random.random() < default_rate
    
    def _generate_new_polyps(self, patient: Patient) -> None:
        """Generate new polyps for a patient in the current year."""
        # Base incidence rate adjusted for age, gender and risk factors
        age_key = min(patient.age // 10 * 10, max(self.settings.polyp_incidence_by_age.keys()))
        base_rate = self.settings.polyp_incidence_by_age[age_key]
        adjusted_rate = base_rate * patient.individual_risk
        
        # Poisson distribution for number of new polyps
        num_new_polyps = np.random.poisson(adjusted_rate)
        
        for _ in range(num_new_polyps):
            location = self._sample_polyp_location()
            patient.add_polyp(location)
    
    def _check_cancer_death(self, patient: Patient) -> bool:
        """Check if patient dies from cancer this year."""
        for cancer in patient.cancers:
            if cancer.stage >= 4:  # Metastatic cancer
                # Treatment can reduce mortality
                mortality_modifier = 0.7 if cancer.treatment_applied else 1.0
                mortality_rate = self.settings.cancer_mortality_by_stage[cancer.stage] * mortality_modifier
                
                if np.random.random() < mortality_rate:
                    return True
        return False
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """Get summary statistics from the simulation.
        
        Returns:
            Dict containing summary statistics
        """
        if not self.results:
            self.results = calculate_statistics(self.patients, self.settings)
            
        return {
            'total_patients': len(self.patients),
            'years_simulated': self.current_year,
            'cancer_incidence': self.results.get('cancer_incidence', 0),
            'cancer_mortality': self.results.get('cancer_mortality', 0),
            'polyps_detected': self.results.get('polyps_detected', 0),
            'advanced_polyps': self.results.get('advanced_polyps', 0),
            'screening_tests': self.results.get('screening_tests', 0),
            'life_years_gained': self.results.get('life_years_gained', 0),
            'simulation_duration': (self.end_time - self.start_time).total_seconds() if self.end_time else None
        }
    
    def get_patient_dataframe(self) -> pd.DataFrame:
        """Convert patient data to pandas DataFrame for analysis.
        
        Returns:
            DataFrame containing patient data
        """
        data = []
        for patient in self.patients:
            patient_data = {
                'id': patient.id,
                'gender': patient.gender,
                'initial_age': patient.age - self.current_year,
                'final_age': patient.age,
                'risk_score': patient.get_risk_score(),
                'death_year': patient.death_year,
                'death_cause': patient.death_cause,
                'polyp_count': len(patient.polyps),
                'cancer_count': len(patient.cancers),
                'screening_count': len(patient.screening_history),
                'treatment_count': len(patient.treatment_history)
            }
            data.append(patient_data)
            
        return pd.DataFrame(data)
    
    def save_results(self, filename: str) -> None:
        """Save simulation results to file.
        
        Args:
            filename: Path to save results
        """
        import json
        
        # Get summary statistics
        summary = self.get_summary_statistics()
        
        # Prepare patient data (simplified for storage)
        patient_data = []
        for patient in self.patients:
            patient_data.append(patient.to_dict())
        
        # Combine data
        output_data = {
            'summary': summary,
            'settings': self.settings.to_dict() if hasattr(self.settings, 'to_dict') else vars(self.settings),
            'patients': patient_data
        }
        
        # Save to file
        with open(filename, 'w') as f:
            json.dump(output_data, f, indent=2)
            
        self.logger.info(f"Results saved to {filename}")
    
    @classmethod
    def load_results(cls, filename: str) -> 'Simulation':
        """Load simulation results from file.
        
        Args:
            filename: Path to load results from
            
        Returns:
            Simulation object with loaded data
        """
        import json
        from ..config.settings import Settings
        
        with open(filename, 'r') as f:
            data = json.load(f)
        
        # Create settings
        settings = Settings()
        if 'settings' in data:
            settings.update(data['settings'])
        
        # Create simulation
        simulation = cls(settings)
        
        # Load patients
        for patient_data in data['patients']:
            patient = Patient.from_dict(patient_data)
            simulation.patients.append(patient)
        
        # Set current year based on patients
        max_age = max(patient.age for patient in simulation.patients)
        min_initial_age = min(patient.age - (patient.death_year or 0) for patient in simulation.patients)
        simulation.current_year = max_age - min_initial_age
        
        return simulation

    def import_patients(self, patients: List[Patient]) -> None:
        """Import existing patients into the simulation.

        Args:
            patients: List of patients to import
        """
        self.logger.info(f"Importing {len(patients)} patients into natural population simulation")
        self.patients.extend(patients)

        # Update current year based on imported patients if needed
        if patients and self.current_year == 0:
            # Estimate current year based on patient ages
            avg_age = sum(p.age for p in patients) / len(patients)
            self.current_year = int(avg_age / 2)  # Rough estimate
