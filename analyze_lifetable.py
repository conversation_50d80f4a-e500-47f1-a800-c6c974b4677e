#!/usr/bin/env python3
"""
分析lifetable.xlsx文件和项目中life table的使用情况
"""

import pandas as pd
import os
import re
from pathlib import Path


def analyze_lifetable_file():
    """分析lifetable.xlsx文件的结构和内容"""
    print("=" * 60)
    print("分析 lifetable.xlsx 文件")
    print("=" * 60)
    
    if not os.path.exists('lifetable.xlsx'):
        print("❌ lifetable.xlsx 文件不存在")
        return None
    
    try:
        # 读取Excel文件的所有工作表
        excel_data = pd.read_excel('lifetable.xlsx', sheet_name=None)
        
        print(f"📊 发现 {len(excel_data)} 个工作表:")
        for sheet_name, df in excel_data.items():
            print(f"  - {sheet_name}: {df.shape[0]} 行 x {df.shape[1]} 列")
            if not df.empty:
                print(f"    列名: {list(df.columns)}")
        
        # 分析主要的生命表数据（Sheet1）
        if 'Sheet1' in excel_data:
            df = excel_data['Sheet1']
            print(f"\n📈 Sheet1 生命表数据分析:")
            print(f"  - 年龄范围: {df['age'].min()} - {df['age'].max()}")
            print(f"  - 数据行数: {len(df)}")
            print(f"  - 列: {list(df.columns)}")
            
            # 显示样本数据
            print(f"\n📋 样本数据 (每10岁):")
            sample_data = df.iloc[::10]
            for _, row in sample_data.iterrows():
                age = int(row['age'])
                male_rate = row['Male']
                female_rate = row['Female']
                print(f"  年龄 {age:3d}: 男性 {male_rate:.6f}, 女性 {female_rate:.6f}")
            
            return df
        else:
            print("❌ 未找到Sheet1工作表")
            return None
            
    except Exception as e:
        print(f"❌ 读取lifetable.xlsx失败: {e}")
        return None


def search_lifetable_usage():
    """搜索项目中life table的使用情况"""
    print("\n" + "=" * 60)
    print("搜索项目中 life table 的使用情况")
    print("=" * 60)
    
    # 搜索关键词
    patterns = [
        r'mortality.*table',
        r'life.*table',
        r'death.*rate',
        r'mortality.*rate',
        r'natural.*death',
        r'lifetable',
        r'mortality_tables'
    ]
    
    found_files = {}
    
    # 搜索Python文件
    for py_file in Path('.').rglob('*.py'):
        try:
            with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
                
                for pattern in patterns:
                    matches = []
                    for i, line in enumerate(lines, 1):
                        if re.search(pattern, line, re.IGNORECASE):
                            matches.append((i, line.strip()))
                    
                    if matches:
                        if str(py_file) not in found_files:
                            found_files[str(py_file)] = []
                        found_files[str(py_file)].extend(matches)
                        
        except Exception as e:
            continue
    
    # 显示结果
    if found_files:
        print(f"🔍 在 {len(found_files)} 个文件中找到相关代码:")
        for file_path, matches in found_files.items():
            print(f"\n📁 {file_path}:")
            # 去重并排序
            unique_matches = list(set(matches))
            unique_matches.sort(key=lambda x: x[0])
            
            for line_num, line in unique_matches[:5]:  # 只显示前5个匹配
                print(f"  {line_num:4d}: {line}")
            
            if len(unique_matches) > 5:
                print(f"  ... 还有 {len(unique_matches) - 5} 个匹配")
    else:
        print("❌ 未找到相关代码")
    
    return found_files


def analyze_mortality_table_structure():
    """分析项目中mortality_tables的数据结构"""
    print("\n" + "=" * 60)
    print("分析 mortality_tables 数据结构")
    print("=" * 60)
    
    # 查找fix_simulation_settings.py中的示例
    if os.path.exists('fix_simulation_settings.py'):
        try:
            with open('fix_simulation_settings.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取mortality_tables定义
            pattern = r'mortality_tables\s*=\s*\{[^}]+\}'
            matches = re.findall(pattern, content, re.DOTALL)
            
            if matches:
                print("📊 在 fix_simulation_settings.py 中找到 mortality_tables 定义:")
                for match in matches:
                    print(f"```python\n{match}\n```")
            else:
                print("❌ 未找到 mortality_tables 定义")
                
        except Exception as e:
            print(f"❌ 读取 fix_simulation_settings.py 失败: {e}")
    
    # 分析数据结构要求
    print(f"\n📋 根据代码分析，mortality_tables 应该具有以下结构:")
    print(f"```python")
    print(f"mortality_tables = {{")
    print(f"    'M': {{  # 男性")
    print(f"        age: mortality_rate,")
    print(f"        # 或者是列表: [rate_age_0, rate_age_1, ...]")
    print(f"    }},")
    print(f"    'F': {{  # 女性")
    print(f"        age: mortality_rate,")
    print(f"        # 或者是列表: [rate_age_0, rate_age_1, ...]")
    print(f"    }}")
    print(f"}}```")


def suggest_lifetable_loader():
    """建议如何创建life table加载器"""
    print("\n" + "=" * 60)
    print("建议的 Life Table 加载器实现")
    print("=" * 60)
    
    print("""
📝 建议创建以下函数来加载 lifetable.xlsx:

```python
def load_lifetable_from_excel(file_path: str = 'lifetable.xlsx') -> Dict[str, Any]:
    \"\"\"
    从Excel文件加载生命表数据
    
    Args:
        file_path: Excel文件路径
        
    Returns:
        包含男性和女性死亡率数据的字典
    \"\"\"
    import pandas as pd
    
    # 读取Excel文件
    df = pd.read_excel(file_path, sheet_name='Sheet1')
    
    # 转换为项目需要的格式
    mortality_tables = {
        'M': {},  # 男性
        'F': {}   # 女性
    }
    
    # 填充数据
    for _, row in df.iterrows():
        age = int(row['age'])
        mortality_tables['M'][age] = float(row['Male'])
        mortality_tables['F'][age] = float(row['Female'])
    
    return mortality_tables
```

🔧 使用位置建议:
1. 在 cmost/utils/file_io.py 中添加此函数
2. 在 cmost/config/settings.py 中调用此函数
3. 在仿真初始化时自动加载 lifetable.xlsx
""")


if __name__ == "__main__":
    # 分析lifetable.xlsx文件
    lifetable_data = analyze_lifetable_file()
    
    # 搜索项目中的使用情况
    usage_files = search_lifetable_usage()
    
    # 分析数据结构
    analyze_mortality_table_structure()
    
    # 提供建议
    suggest_lifetable_loader()
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
