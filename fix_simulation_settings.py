#!/usr/bin/env python3
"""
修复仿真设置问题，确保Settings对象包含所有必需的属性
"""

import os
import sys
import json
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_complete_settings():
    """创建包含所有必需属性的完整设置对象"""
    print("=" * 60)
    print("修复CMOST仿真设置")
    print("=" * 60)

    try:
        from cmost.config.settings import Settings

        # 创建设置对象
        settings = Settings()

        # 添加缺失的风险因子分布
        print("1. 添加风险因子分布...")

        # 创建简单的风险因子分布类
        class SimpleDistribution:
            def __init__(self, mean=1.0, std=0.2, min_val=0.5, max_val=3.0):
                self.mean = mean
                self.std = std
                self.min_val = min_val
                self.max_val = max_val

            def sample(self):
                import numpy as np
                value = np.random.normal(self.mean, self.std)
                return max(self.min_val, min(self.max_val, value))

        # 设置风险因子分布
        risk_factor_distributions = {
            'family_history': SimpleDistribution(mean=1.5, std=0.5, min_val=1.0, max_val=3.0),
            'smoking': SimpleDistribution(mean=1.2, std=0.3, min_val=1.0, max_val=2.0),
            'obesity': SimpleDistribution(mean=1.1, std=0.2, min_val=1.0, max_val=1.5),
            'diabetes': SimpleDistribution(mean=1.1, std=0.2, min_val=1.0, max_val=1.5),
            'alcohol': SimpleDistribution(mean=1.05, std=0.1, min_val=1.0, max_val=1.3)
        }

        settings.risk_factor_distributions = risk_factor_distributions
        print("✓ 风险因子分布设置完成")

        # 添加息肉患病率数据
        print("2. 添加息肉患病率数据...")
        polyp_prevalence_by_age = {
            20: 0.01,  # 20-29岁
            30: 0.02,  # 30-39岁
            40: 0.05,  # 40-49岁
            50: 0.10,  # 50-59岁
            60: 0.15,  # 60-69岁
            70: 0.20,  # 70-79岁
            80: 0.25,  # 80+岁
        }
        settings.polyp_prevalence_by_age = polyp_prevalence_by_age
        print("✓ 息肉患病率数据设置完成")
        
        # 添加锯齿状病变患病率数据
        print("3. 添加锯齿状病变患病率数据...")
        serrated_prevalence_by_age = {
            20: 0.005,
            30: 0.01,
            40: 0.02,
            50: 0.03,
            60: 0.04,
            70: 0.05,
            80: 0.06,
        }
        settings.serrated_prevalence_by_age = serrated_prevalence_by_age
        print("✓ 锯齿状病变患病率数据设置完成")

        # 添加息肉位置分布数据
        print("4. 添加息肉位置分布数据...")
        polyp_location_distribution = {
            1: 0.15,  # 直肠
            2: 0.20,  # 乙状结肠
            3: 0.15,  # 降结肠
            4: 0.20,  # 横结肠
            5: 0.20,  # 升结肠
            6: 0.10   # 盲肠
        }
        settings.polyp_location_distribution = polyp_location_distribution
        print("✓ 息肉位置分布数据设置完成")

        # 添加死亡率表数据
        print("5. 添加死亡率表数据...")
        mortality_tables = {
            'M': {  # 男性
                20: 0.001, 30: 0.002, 40: 0.003, 50: 0.005,
                60: 0.010, 70: 0.020, 80: 0.050, 90: 0.100
            },
            'F': {  # 女性
                20: 0.0008, 30: 0.0015, 40: 0.0025, 50: 0.004,
                60: 0.008, 70: 0.015, 80: 0.040, 90: 0.080
            }
        }
        settings.mortality_tables = mortality_tables
        print("✓ 死亡率表数据设置完成")

        # 设置基本仿真参数
        print("6. 设置基本仿真参数...")
        settings.set('Number_patients', 1000)
        settings.set('Simulation_years', 10)
        settings.set('start_age', 20)
        settings.set('end_age', 85)
        settings.set('screening_start_age', 50)
        settings.set('screening_end_age', 75)
        settings.set('screening_interval', 10)
        settings.set('enable_screening', True)
        settings.set('random_seed', 12345)

        # 设置模型参数
        settings.set('ModelParameters.male_proportion', 0.51)

        print("✓ 基本仿真参数设置完成")

        return settings
        
    except Exception as e:
        print(f"❌ 设置创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_fixed_simulation():
    """测试修复后的仿真"""
    print("\n" + "=" * 60)
    print("测试修复后的仿真")
    print("=" * 60)
    
    try:
        # 创建完整的设置
        settings = create_complete_settings()
        if settings is None:
            return False
        
        # 导入仿真模块
        from cmost.core.simulation import Simulation
        from cmost.utils.file_io import save_simulation_results
        
        print("\n5. 创建仿真对象...")
        simulation = Simulation(settings)
        print("✓ 仿真对象创建成功")
        
        print("\n6. 初始化人群...")
        simulation.initialize_population()
        print(f"✓ 人群初始化完成，共 {len(simulation.patients)} 个患者")
        
        # 检查患者是否正确创建
        valid_patients = [p for p in simulation.patients if p is not None]
        print(f"✓ 有效患者数量: {len(valid_patients)}")
        
        if len(valid_patients) == 0:
            print("❌ 没有有效患者，无法继续仿真")
            return False
        
        print("\n7. 运行仿真...")
        start_time = time.time()
        
        # 运行仿真
        results = simulation.run(years=settings.get('Simulation_years'))
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✓ 仿真运行完成，耗时 {duration:.2f} 秒")
        
        # 获取统计结果
        print("\n8. 生成统计结果...")
        stats = simulation.get_summary_statistics()
        print("✓ 统计结果生成完成")
        
        # 显示主要结果
        print("\n9. 主要结果:")
        print("-" * 40)
        if isinstance(stats, dict):
            for key, value in stats.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value}")
        
        # 保存结果
        print("\n10. 保存结果...")
        results_dir = "results"
        os.makedirs(results_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_data = {
            'timestamp': timestamp,
            'settings': {
                'Number_patients': settings.get('Number_patients'),
                'Simulation_years': settings.get('Simulation_years'),
                'random_seed': settings.get('random_seed'),
                'screening_enabled': settings.get('enable_screening')
            },
            'statistics': stats,
            'simulation_info': {
                'duration_seconds': duration,
                'total_patients': len(simulation.patients),
                'valid_patients': len(valid_patients),
                'completed_successfully': True
            }
        }
        
        # 保存为JSON格式
        json_file = os.path.join(results_dir, f"fixed_simulation_results_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 结果已保存到: {json_file}")
        
        # 验证文件
        if os.path.exists(json_file):
            file_size = os.path.getsize(json_file)
            print(f"✓ 文件验证成功 (大小: {file_size} 字节)")
        else:
            print("❌ 文件保存失败")
            return False
        
        print("\n" + "=" * 60)
        print("修复测试完成！")
        print("=" * 60)
        print("✅ 设置问题已修复")
        print("✅ 仿真运行成功")
        print("✅ 结果输出正常")
        print(f"\n📁 结果文件: {os.path.abspath(json_file)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_simulation()
    sys.exit(0 if success else 1)
