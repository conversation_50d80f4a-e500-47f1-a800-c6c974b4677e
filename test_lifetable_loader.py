#!/usr/bin/env python3
"""
测试新的life table加载函数
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from cmost.utils.file_io import (
    load_lifetable_from_excel,
    load_lifetable_as_list,
    validate_lifetable_file,
    save_lifetable_to_excel
)


def test_lifetable_validation():
    """测试life table文件验证"""
    print("=" * 60)
    print("测试 Life Table 文件验证")
    print("=" * 60)
    
    # 验证lifetable.xlsx文件
    result = validate_lifetable_file('lifetable.xlsx')
    
    print(f"📊 验证结果:")
    print(f"  有效性: {'✓ 有效' if result['valid'] else '❌ 无效'}")
    
    if result['errors']:
        print(f"  错误:")
        for error in result['errors']:
            print(f"    - {error}")
    
    if result['warnings']:
        print(f"  警告:")
        for warning in result['warnings']:
            print(f"    - {warning}")
    
    if result['summary']:
        print(f"  数据摘要:")
        for key, value in result['summary'].items():
            print(f"    {key}: {value}")
    
    return result['valid']


def test_lifetable_loading():
    """测试life table加载功能"""
    print("\n" + "=" * 60)
    print("测试 Life Table 加载功能")
    print("=" * 60)
    
    try:
        # 测试字典格式加载
        print("📖 测试字典格式加载...")
        mortality_dict = load_lifetable_from_excel('lifetable.xlsx')
        
        print(f"✓ 成功加载字典格式数据")
        print(f"  男性数据点数: {len(mortality_dict['M'])}")
        print(f"  女性数据点数: {len(mortality_dict['F'])}")
        
        # 显示样本数据
        sample_ages = [0, 20, 50, 80, 100]
        print(f"  样本数据:")
        for age in sample_ages:
            if age in mortality_dict['M']:
                male_rate = mortality_dict['M'][age]
                female_rate = mortality_dict['F'][age]
                print(f"    年龄 {age:3d}: 男性 {male_rate:.6f}, 女性 {female_rate:.6f}")
        
        # 测试列表格式加载
        print(f"\n📖 测试列表格式加载...")
        mortality_list = load_lifetable_as_list('lifetable.xlsx')
        
        print(f"✓ 成功加载列表格式数据")
        print(f"  男性列表长度: {len(mortality_list['M'])}")
        print(f"  女性列表长度: {len(mortality_list['F'])}")
        
        # 验证数据一致性
        print(f"\n🔍 验证数据一致性...")
        consistent = True
        for age in sample_ages:
            if age < len(mortality_list['M']):
                dict_male = mortality_dict['M'].get(age, 0)
                list_male = mortality_list['M'][age]
                if abs(dict_male - list_male) > 1e-10:
                    print(f"❌ 年龄 {age} 男性数据不一致: {dict_male} vs {list_male}")
                    consistent = False
        
        if consistent:
            print(f"✓ 字典格式和列表格式数据一致")
        
        return mortality_dict, mortality_list
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return None, None


def test_lifetable_saving():
    """测试life table保存功能"""
    print("\n" + "=" * 60)
    print("测试 Life Table 保存功能")
    print("=" * 60)
    
    try:
        # 先加载数据
        mortality_dict = load_lifetable_from_excel('lifetable.xlsx')
        
        # 保存为新文件
        output_file = 'test_lifetable_output.xlsx'
        save_lifetable_to_excel(mortality_dict, output_file)
        
        # 验证保存的文件
        print(f"\n🔍 验证保存的文件...")
        if os.path.exists(output_file):
            # 重新加载并比较
            reloaded_data = load_lifetable_from_excel(output_file)
            
            # 比较几个样本点
            sample_ages = [0, 20, 50, 80]
            all_match = True
            
            for age in sample_ages:
                if age in mortality_dict['M'] and age in reloaded_data['M']:
                    orig_male = mortality_dict['M'][age]
                    reload_male = reloaded_data['M'][age]
                    if abs(orig_male - reload_male) > 1e-10:
                        print(f"❌ 年龄 {age} 男性数据不匹配")
                        all_match = False
            
            if all_match:
                print(f"✓ 保存和重新加载的数据一致")
            
            # 清理测试文件
            os.remove(output_file)
            print(f"✓ 清理测试文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存测试失败: {e}")


def test_integration_with_simulation():
    """测试与仿真系统的集成"""
    print("\n" + "=" * 60)
    print("测试与仿真系统的集成")
    print("=" * 60)
    
    try:
        # 加载life table数据
        mortality_tables = load_lifetable_from_excel('lifetable.xlsx')
        
        # 模拟仿真系统中的使用方式
        print("🔧 模拟仿真系统使用...")
        
        # 测试访问特定年龄的死亡率
        test_patient_age = 65
        test_patient_gender = 'M'
        
        if test_patient_age in mortality_tables[test_patient_gender]:
            mortality_rate = mortality_tables[test_patient_gender][test_patient_age]
            print(f"✓ 成功获取 {test_patient_age} 岁{test_patient_gender}性患者的死亡率: {mortality_rate:.6f}")
        else:
            print(f"❌ 未找到 {test_patient_age} 岁的死亡率数据")
        
        # 测试年龄范围检查
        max_age_m = max(mortality_tables['M'].keys())
        max_age_f = max(mortality_tables['F'].keys())
        print(f"✓ 男性最大年龄: {max_age_m}")
        print(f"✓ 女性最大年龄: {max_age_f}")
        
        # 测试边界情况
        print(f"\n🔍 测试边界情况...")
        
        # 测试超出范围的年龄
        test_age_over = max_age_m + 10
        if test_age_over not in mortality_tables['M']:
            print(f"✓ 正确处理超出范围的年龄 {test_age_over}")
        
        # 建议如何在仿真中使用
        print(f"\n💡 在仿真中的使用建议:")
        print(f"```python")
        print(f"# 在Settings类中加载life table")
        print(f"from cmost.utils import load_lifetable_from_excel")
        print(f"")
        print(f"# 加载数据")
        print(f"mortality_tables = load_lifetable_from_excel('lifetable.xlsx')")
        print(f"settings.mortality_tables = mortality_tables")
        print(f"")
        print(f"# 在仿真中使用")
        print(f"mortality_rate = settings.mortality_tables[patient.gender][patient.age]")
        print(f"```")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Life Table 加载器测试")
    print("=" * 60)
    
    # 检查lifetable.xlsx是否存在
    if not os.path.exists('lifetable.xlsx'):
        print("❌ lifetable.xlsx 文件不存在，请确保文件在当前目录中")
        return
    
    # 运行所有测试
    tests_passed = 0
    total_tests = 4
    
    # 测试1: 文件验证
    if test_lifetable_validation():
        tests_passed += 1
    
    # 测试2: 数据加载
    dict_data, list_data = test_lifetable_loading()
    if dict_data is not None and list_data is not None:
        tests_passed += 1
    
    # 测试3: 数据保存
    try:
        test_lifetable_saving()
        tests_passed += 1
    except:
        pass
    
    # 测试4: 集成测试
    if test_integration_with_simulation():
        tests_passed += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"通过测试: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过！Life table加载器已准备就绪。")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    main()
