"""
Birth cohort simulation module for CMOST.

This module implements birth cohort dynamic simulation architecture,
complementing the existing natural population cohort simulation.
"""

from typing import Dict, List, Optional, Tuple, Any
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import logging

from ..models.patient import Patient
from ..config.settings import Settings
from .population import PopulationGenerator


@dataclass
class BirthCohort:
    """Represents a birth cohort in the simulation."""
    
    birth_year: int
    size: int
    patients: List[Patient] = field(default_factory=list)
    
    # Cohort characteristics
    male_proportion: float = 0.5
    risk_factor_distribution: Dict[str, Any] = field(default_factory=dict)
    
    # Tracking
    created_year: int = field(default_factory=lambda: datetime.now().year)
    
    def __post_init__(self):
        """Initialize derived attributes."""
        self.alive_patients = self.size
        self.deaths_by_cause = {'natural': 0, 'cancer': 0, 'other': 0}


class BirthCohortSimulation:
    """Birth cohort simulation engine."""
    
    def __init__(self, settings: Settings) -> None:
        """Initialize birth cohort simulation.
        
        Args:
            settings: Simulation settings
        """
        self.settings: Settings = settings
        self.cohorts: Dict[int, BirthCohort] = {}
        self.current_year: int = self._get_setting('Simulation.StartYear', 2000)
        self.end_year: int = self._get_setting('Simulation.EndYear', 2100)
        
        # Birth rate settings
        self.annual_births: int = self._get_setting('BirthCohort.AnnualBirths', 1000)
        self.birth_rate_trend: float = self._get_setting('BirthCohort.BirthRateTrend', 0.0)  # Annual change
        
        # Population generator for creating new cohorts
        from .population import PopulationGenerator
        self.pop_generator: PopulationGenerator = PopulationGenerator(settings)
        
        # Logging
        self.logger: logging.Logger = logging.getLogger("CMOST_BirthCohort")
        
    def _get_setting(self, key: str, default=None):
        """Helper method to get settings with dot notation."""
        keys = key.split('.')
        value = self.settings.settings
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
        
    def create_birth_cohort(self, birth_year: int, size: int) -> BirthCohort:
        """Create a new birth cohort.
        
        Args:
            birth_year: Year of birth for this cohort
            size: Number of individuals in the cohort
            
        Returns:
            BirthCohort: New birth cohort
        """
        self.logger.info(f"Creating birth cohort for year {birth_year} with {size} individuals")
        
        # Create cohort
        cohort = BirthCohort(
            birth_year=birth_year,
            size=size,
            male_proportion=self._get_setting('ModelParameters.male_proportion', 0.5)
        )
        
        # Generate patients for this cohort
        for i in range(size):
            # All patients start at age 0
            gender = 'M' if np.random.random() < cohort.male_proportion else 'F'
            
            # Generate risk factors (some may be determined at birth, others later)
            risk_factors = self._generate_birth_risk_factors(gender)
            
            patient = Patient(
                id=f"{birth_year}_{i}",  # Unique ID with birth year
                age=0,
                gender=gender,
                risk_factors=risk_factors
            )
            
            # Set birth year
            patient.birth_year = birth_year
            
            cohort.patients.append(patient)
        
        return cohort
        
    def _generate_birth_risk_factors(self, gender: str) -> Dict[str, float]:
        """Generate risk factors determined at birth.
        
        Args:
            gender: Patient gender
            
        Returns:
            Dict of risk factors
        """
        risk_factors = {}
        
        # Genetic/family history risk (determined at birth)
        family_history_prevalence = self._get_setting('RiskFactors.family_history_prevalence', 0.10)
        has_family_history = np.random.random() < family_history_prevalence
        risk_factors['family_history'] = 2.0 if has_family_history else 1.0
        
        # Genetic predisposition (simplified)
        genetic_risk = np.random.lognormal(0, 0.2)  # Log-normal distribution around 1.0
        risk_factors['genetic'] = max(0.5, min(2.0, genetic_risk))
        
        # Other factors will be assigned later in life
        risk_factors['smoking'] = 1.0  # Will be updated when patient reaches smoking age
        risk_factors['bmi'] = 1.0      # Will be updated based on lifestyle
        risk_factors['physical_activity'] = 1.0  # Will be updated
        risk_factors['diet'] = 1.0     # Will be updated
        
        return risk_factors
        
    def update_cohort_risk_factors(self, cohort: BirthCohort, current_year: int) -> None:
        """Update risk factors for patients in cohort based on their current age.
        
        Args:
            cohort: Birth cohort to update
            current_year: Current simulation year
        """
        for patient in cohort.patients:
            if not patient.is_alive(current_year):
                continue
                
            current_age = current_year - cohort.birth_year
            patient.age = current_age
            
            # Update age-dependent risk factors
            self._update_age_dependent_risks(patient, current_age)
            
            # Recalculate individual risk
            patient.individual_risk = patient._calculate_individual_risk()
            
    def _update_age_dependent_risks(self, patient: Patient, age: int) -> None:
        """Update risk factors that change with age.
        
        Args:
            patient: Patient to update
            age: Current age
        """
        # Smoking risk (starts around age 15-20)
        if age >= 15 and patient.risk_factors['smoking'] == 1.0:
            smoking_prob = 0.25 if patient.gender == 'M' else 0.15
            if np.random.random() < smoking_prob:
                patient.risk_factors['smoking'] = 1.5
                
        # BMI risk (becomes relevant in adulthood)
        if age >= 18:
            # Simulate BMI changes over time
            if patient.gender == 'M':
                base_bmi = 27.5 + (age - 18) * 0.1  # Gradual increase with age
            else:
                base_bmi = 26.5 + (age - 18) * 0.1
                
            bmi = np.random.normal(base_bmi, 4.0)
            bmi = max(16, min(45, bmi))
            
            if bmi < 18.5:
                patient.risk_factors['bmi'] = 0.9
            elif bmi < 25:
                patient.risk_factors['bmi'] = 1.0
            elif bmi < 30:
                patient.risk_factors['bmi'] = 1.2
            else:
                patient.risk_factors['bmi'] = 1.5
                
        # Physical activity (decreases with age)
        if age >= 20:
            if age < 40:
                activity_prob = [0.20, 0.40, 0.30, 0.10]  # sedentary, light, moderate, high
            elif age < 65:
                activity_prob = [0.30, 0.40, 0.25, 0.05]
            else:
                activity_prob = [0.50, 0.35, 0.15, 0.00]
                
            activity_levels = [1.3, 1.1, 1.0, 0.9]
            activity_idx = np.random.choice(4, p=activity_prob)
            patient.risk_factors['physical_activity'] = activity_levels[activity_idx]
            
    def add_annual_birth_cohort(self, year: int) -> None:
        """Add a new birth cohort for the given year.
        
        Args:
            year: Year to add birth cohort for
        """
        # Calculate births for this year (with trend)
        years_from_start = year - self.current_year
        births = int(self.annual_births * (1 + self.birth_rate_trend) ** years_from_start)
        
        # Create and store cohort
        cohort = self.create_birth_cohort(year, births)
        self.cohorts[year] = cohort
        
    def simulate_year(self, year: int) -> None:
        """Simulate one year for all birth cohorts.
        
        Args:
            year: Year to simulate
        """
        self.logger.info(f"Simulating birth cohort year {year}")
        
        # Add new birth cohort
        self.add_annual_birth_cohort(year)
        
        # Update all existing cohorts
        for birth_year, cohort in self.cohorts.items():
            if birth_year <= year:  # Only process cohorts that have been born
                self.update_cohort_risk_factors(cohort, year)
                self._simulate_cohort_year(cohort, year)
                
    def _simulate_cohort_year(self, cohort: BirthCohort, current_year: int) -> None:
        """Simulate one year for a specific cohort.
        
        Args:
            cohort: Birth cohort to simulate
            current_year: Current simulation year
        """
        for patient in cohort.patients:
            if not patient.is_alive(current_year):
                continue
                
            # Check for natural death
            if self._check_natural_death(patient, current_year):
                patient.death_year = current_year
                patient.death_cause = "natural"
                cohort.deaths_by_cause['natural'] += 1
                cohort.alive_patients -= 1
                continue
                
            # Generate new polyps (age-dependent)
            if patient.age >= 20:  # Polyps typically start appearing after age 20
                self._generate_new_polyps(patient)
                
            # Progress existing conditions
            if hasattr(self, 'progression_model'):
                patient.progress(1, self.progression_model)
                
            # Check for cancer death
            if self._check_cancer_death(patient):
                patient.death_year = current_year
                patient.death_cause = "cancer"
                cohort.deaths_by_cause['cancer'] += 1
                cohort.alive_patients -= 1
                
    def _check_natural_death(self, patient: Patient, current_year: int) -> bool:
        """Check if patient dies from natural causes this year."""
        # Use life tables if available
        if hasattr(self.settings, 'mortality_tables') and self.settings.mortality_tables:
            try:
                # Use the safer method from Settings class if available
                if hasattr(self.settings, 'get_mortality_rate'):
                    mortality_rate = self.settings.get_mortality_rate(patient.age, patient.gender)
                    return np.random.random() < mortality_rate
                else:
                    # Legacy approach with error handling
                    mortality_table = self.settings.mortality_tables.get(patient.gender, None)
                    if mortality_table:
                        if isinstance(mortality_table, dict):
                            mortality_rate = mortality_table.get(patient.age, 0.001)
                        else:
                            # Handle legacy list format
                            if patient.age < len(mortality_table):
                                mortality_rate = mortality_table[patient.age]
                            else:
                                mortality_rate = mortality_table[-1]  # Use last available rate
                        return np.random.random() < mortality_rate
            except Exception as e:
                # Log warning and fall through to default calculation
                print(f"Warning: Error accessing mortality rate for age {patient.age}, gender {patient.gender}: {e}")
                
        # Fallback to age-based mortality
        if patient.age < 1:
            mortality_rate = 0.005  # Infant mortality
        elif patient.age < 20:
            mortality_rate = 0.0005  # Very low for young people
        elif patient.age < 40:
            mortality_rate = 0.001
        elif patient.age < 60:
            mortality_rate = 0.005
        elif patient.age < 80:
            mortality_rate = 0.02
        else:
            mortality_rate = 0.1
            
        return np.random.random() < mortality_rate
        
    def _generate_new_polyps(self, patient: Patient) -> None:
        """Generate new polyps for patient based on age and risk."""
        # Age-dependent polyp generation rate
        if patient.age < 30:
            base_rate = 0.001
        elif patient.age < 50:
            base_rate = 0.005
        elif patient.age < 70:
            base_rate = 0.02
        else:
            base_rate = 0.05
            
        # Adjust for individual risk
        adjusted_rate = base_rate * patient.individual_risk
        
        # Poisson process for polyp generation
        num_new_polyps = np.random.poisson(adjusted_rate)
        
        for _ in range(num_new_polyps):
            location = np.random.randint(1, 7)  # 6 colon segments
            size = np.random.exponential(0.3)   # Small initial size
            patient.add_polyp(location, size)
            
    def _check_cancer_death(self, patient: Patient) -> bool:
        """Check if patient dies from cancer this year."""
        if not patient.cancers:
            return False
            
        for cancer in patient.cancers:
            if cancer.stage >= 9:  # Advanced cancer stages
                mortality_rate = 0.3 if cancer.stage == 9 else 0.7  # Stage III vs IV
                if cancer.treatment_applied:
                    mortality_rate *= 0.6  # Treatment reduces mortality
                    
                if np.random.random() < mortality_rate:
                    return True
                    
        return False
        
    def get_cohort_statistics(self) -> Dict[str, Any]:
        """Get statistics for all birth cohorts.
        
        Returns:
            Dictionary containing cohort statistics
        """
        total_deaths = 0
        deaths_by_cause = {'natural': 0, 'cancer': 0, 'other': 0}
        
        # Calculate totals across all cohorts
        for cohort in self.cohorts.values():
            for cause in deaths_by_cause:
                deaths_by_cause[cause] += cohort.deaths_by_cause[cause]
                total_deaths += cohort.deaths_by_cause[cause]
        
        stats = {
            'total_cohorts': len(self.cohorts),
            'total_births': sum(cohort.size for cohort in self.cohorts.values()),
            'total_alive': sum(cohort.alive_patients for cohort in self.cohorts.values()),
            'total_deaths': total_deaths,  # Added missing total_deaths
            'deaths_by_cause': deaths_by_cause,
            'cohorts_by_year': {}
        }
        
        for birth_year, cohort in self.cohorts.items():
            stats['cohorts_by_year'][birth_year] = {
                'size': cohort.size,
                'alive': cohort.alive_patients,
                'deaths': cohort.deaths_by_cause
            }
            
        return stats
        
    def run(self, years: int) -> Dict[str, Any]:
        """Run birth cohort simulation for specified number of years.
        
        Args:
            years: Number of years to simulate
            
        Returns:
            Dictionary containing simulation results
        """
        self.logger.info(f"Starting birth cohort simulation for {years} years")
        
        for year_offset in range(years):
            current_year = self.current_year + year_offset
            self.simulate_year(current_year)
            
        # Return final statistics
        return self.get_cohort_statistics()

    def get_results(self) -> Dict[str, Any]:
        """Get simulation results in standard format.

        Returns:
            Dictionary containing simulation results
        """
        stats = self.get_cohort_statistics()

        # Calculate cancer cases from all cohorts
        total_cancer_cases = 0
        total_screenings_performed = 0
        
        for cohort in self.cohorts.values():
            for patient in cohort.patients:
                # Count cancer cases
                total_cancer_cases += len(patient.cancers)
                
                # Count screenings performed
                total_screenings_performed += len(patient.screening_history)

        # Calculate total deaths
        total_deaths = (stats['deaths_by_cause']['natural'] + 
                       stats['deaths_by_cause']['cancer'] + 
                       stats['deaths_by_cause']['other'])

        # Convert to standard results format
        return {
            'total_patients': stats['total_births'],  # Fixed: use total_births instead of non-existent total_patients
            'cancer_cases': total_cancer_cases,
            'deaths': total_deaths,  # Fixed: calculate from deaths_by_cause
            'screenings_performed': total_screenings_performed,
            'cohort_statistics': stats
        }

    def get_all_patients(self) -> List[Patient]:
        """Get all patients from all cohorts.

        Returns:
            List of all patients
        """
        all_patients = []
        for cohort in self.cohorts.values():
            all_patients.extend(cohort.patients)
        return all_patients

    def import_existing_patients(self, patients: List[Patient], current_year: int) -> None:
        """Import existing patients into birth cohort structure.

        Args:
            patients: List of patients to import
            current_year: Current simulation year
        """
        self.logger.info(f"Importing {len(patients)} existing patients into birth cohort structure")

        # Group patients by birth year
        patients_by_birth_year = {}
        for patient in patients:
            birth_year = current_year - patient.age
            if birth_year not in patients_by_birth_year:
                patients_by_birth_year[birth_year] = []
            patients_by_birth_year[birth_year].append(patient)

        # Create cohorts for each birth year
        for birth_year, cohort_patients in patients_by_birth_year.items():
            if birth_year not in self.cohorts:
                cohort = BirthCohort(
                    birth_year=birth_year,
                    size=len(cohort_patients)
                )
                self.cohorts[birth_year] = cohort
            else:
                cohort = self.cohorts[birth_year]
                cohort.size += len(cohort_patients)

            # Add patients to cohort
            cohort.patients.extend(cohort_patients)

            # Update alive count
            alive_count = sum(1 for p in cohort_patients if p.death_year is None or p.death_year > current_year)
            cohort.alive_patients = alive_count
