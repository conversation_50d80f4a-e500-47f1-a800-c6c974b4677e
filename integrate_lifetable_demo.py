#!/usr/bin/env python3
"""
演示如何在CMOST仿真系统中集成新的life table加载器
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from cmost.utils import load_lifetable_from_excel, validate_lifetable_file
from cmost.config.settings import Settings


def demo_lifetable_integration():
    """演示life table集成到仿真系统"""
    print("=" * 70)
    print("CMOST Life Table 集成演示")
    print("=" * 70)
    
    # 步骤1: 验证life table文件
    print("📋 步骤1: 验证 lifetable.xlsx 文件")
    print("-" * 50)
    
    validation_result = validate_lifetable_file('lifetable.xlsx')
    
    if not validation_result['valid']:
        print("❌ Life table文件验证失败:")
        for error in validation_result['errors']:
            print(f"  - {error}")
        return False
    
    print("✓ Life table文件验证通过")
    print(f"  年龄范围: {validation_result['summary']['age_range']}")
    print(f"  数据点数: {validation_result['summary']['total_ages']}")
    
    # 步骤2: 加载life table数据
    print(f"\n📖 步骤2: 加载 life table 数据")
    print("-" * 50)
    
    try:
        mortality_tables = load_lifetable_from_excel('lifetable.xlsx')
        print("✓ Life table数据加载成功")
        
        # 显示一些关键统计信息
        male_ages = list(mortality_tables['M'].keys())
        female_ages = list(mortality_tables['F'].keys())
        
        print(f"  男性数据: {min(male_ages)} - {max(male_ages)} 岁")
        print(f"  女性数据: {min(female_ages)} - {max(female_ages)} 岁")
        
        # 显示不同年龄段的死亡率
        key_ages = [20, 40, 60, 80, 100]
        print(f"  关键年龄死亡率:")
        for age in key_ages:
            if age in mortality_tables['M']:
                male_rate = mortality_tables['M'][age]
                female_rate = mortality_tables['F'][age]
                print(f"    {age:3d}岁: 男性 {male_rate:.6f}, 女性 {female_rate:.6f}")
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        return False
    
    # 步骤3: 集成到Settings类
    print(f"\n⚙️  步骤3: 集成到 Settings 类")
    print("-" * 50)
    
    try:
        # 创建Settings实例
        settings = Settings()
        
        # 将life table数据设置到settings中
        settings.mortality_tables = mortality_tables
        
        print("✓ Life table数据已设置到Settings类")
        print(f"  Settings中的mortality_tables类型: {type(settings.mortality_tables)}")
        print(f"  包含性别: {list(settings.mortality_tables.keys())}")
        
        # 验证数据可以正确访问
        test_age = 65
        test_gender = 'M'
        
        if hasattr(settings, 'mortality_tables') and settings.mortality_tables:
            if test_gender in settings.mortality_tables:
                if test_age in settings.mortality_tables[test_gender]:
                    rate = settings.mortality_tables[test_gender][test_age]
                    print(f"✓ 测试访问: {test_age}岁{test_gender}性死亡率 = {rate:.6f}")
                else:
                    print(f"⚠️  年龄 {test_age} 不在数据范围内")
            else:
                print(f"⚠️  性别 {test_gender} 不在数据中")
        else:
            print("❌ mortality_tables未正确设置")
            return False
        
    except Exception as e:
        print(f"❌ Settings集成失败: {e}")
        return False
    
    # 步骤4: 模拟仿真中的使用
    print(f"\n🎯 步骤4: 模拟仿真中的使用")
    print("-" * 50)
    
    try:
        # 模拟患者数据
        test_patients = [
            {'id': 1, 'age': 45, 'gender': 'M'},
            {'id': 2, 'age': 67, 'gender': 'F'},
            {'id': 3, 'age': 82, 'gender': 'M'},
            {'id': 4, 'age': 29, 'gender': 'F'},
        ]
        
        print("模拟为患者设置自然死亡年份:")
        
        for patient in test_patients:
            age = patient['age']
            gender = patient['gender']
            
            # 获取当前年龄的死亡率
            if age in settings.mortality_tables[gender]:
                mortality_rate = settings.mortality_tables[gender][age]
                
                # 模拟计算预期死亡年龄（简化版本）
                # 实际仿真中会使用更复杂的算法
                import random
                random.seed(42)  # 为了可重现的结果
                
                death_age = age
                current_age = age
                while current_age < 150:  # 最大年龄限制
                    if current_age in settings.mortality_tables[gender]:
                        rate = settings.mortality_tables[gender][current_age]
                        if random.random() < rate:
                            death_age = current_age
                            break
                    current_age += 1
                
                years_to_death = death_age - age
                print(f"  患者{patient['id']} ({age}岁{gender}): 预期{years_to_death}年后去世 (死亡率: {mortality_rate:.6f})")
            else:
                print(f"  患者{patient['id']} ({age}岁{gender}): 年龄超出数据范围")
        
    except Exception as e:
        print(f"❌ 仿真使用模拟失败: {e}")
        return False
    
    # 步骤5: 提供使用建议
    print(f"\n💡 步骤5: 使用建议和最佳实践")
    print("-" * 50)
    
    print("""
🔧 在实际仿真中的集成建议:

1. 在Settings类初始化时自动加载:
   ```python
   class Settings:
       def __init__(self):
           # ... 其他初始化代码 ...
           self._load_mortality_tables()
       
       def _load_mortality_tables(self):
           try:
               from cmost.utils import load_lifetable_from_excel
               self.mortality_tables = load_lifetable_from_excel('lifetable.xlsx')
           except Exception as e:
               # 使用默认值或抛出错误
               self.mortality_tables = self._get_default_mortality_tables()
   ```

2. 在仿真核心模块中使用:
   ```python
   def _set_natural_death_year(self, patient):
       mortality_table = self.settings.mortality_tables[patient.gender]
       # 使用life table数据计算死亡年份
   ```

3. 错误处理建议:
   - 检查年龄是否在数据范围内
   - 为超出范围的年龄提供默认值
   - 验证性别参数的有效性

4. 性能优化:
   - 考虑将life table数据缓存
   - 对于大规模仿真，可以预计算常用年龄段的数据
""")
    
    print("✅ Life table集成演示完成!")
    return True


def compare_with_hardcoded_data():
    """比较新加载的数据与硬编码数据的差异"""
    print(f"\n📊 附加分析: 与硬编码数据的比较")
    print("-" * 50)
    
    # 硬编码数据（来自fix_simulation_settings.py）
    hardcoded_mortality = {
        'M': {
            20: 0.001, 30: 0.002, 40: 0.003, 50: 0.005,
            60: 0.010, 70: 0.020, 80: 0.050, 90: 0.100
        },
        'F': {
            20: 0.0008, 30: 0.0015, 40: 0.0025, 50: 0.004,
            60: 0.008, 70: 0.015, 80: 0.040, 90: 0.080
        }
    }
    
    # 加载真实数据
    real_mortality = load_lifetable_from_excel('lifetable.xlsx')
    
    print("硬编码数据 vs 真实Life Table数据比较:")
    print("年龄  性别  硬编码      真实数据     差异")
    print("-" * 45)
    
    for gender in ['M', 'F']:
        gender_name = '男性' if gender == 'M' else '女性'
        for age in sorted(hardcoded_mortality[gender].keys()):
            hardcoded_rate = hardcoded_mortality[gender][age]
            real_rate = real_mortality[gender].get(age, 0)
            diff = abs(hardcoded_rate - real_rate)
            diff_pct = (diff / hardcoded_rate * 100) if hardcoded_rate > 0 else 0
            
            print(f"{age:3d}  {gender_name}  {hardcoded_rate:.6f}  {real_rate:.6f}  {diff_pct:6.1f}%")
    
    print(f"\n💭 分析结论:")
    print(f"- 硬编码数据是简化的示例数据")
    print(f"- 真实life table数据更加精确和完整")
    print(f"- 建议在生产环境中使用真实的life table数据")


def main():
    """主函数"""
    print("🚀 启动 CMOST Life Table 集成演示")
    
    # 检查必要文件
    if not os.path.exists('lifetable.xlsx'):
        print("❌ 错误: lifetable.xlsx 文件不存在")
        print("请确保lifetable.xlsx文件在当前目录中")
        return
    
    # 运行主要演示
    success = demo_lifetable_integration()
    
    if success:
        # 运行比较分析
        compare_with_hardcoded_data()
        
        print(f"\n🎉 演示完成!")
        print(f"Life table加载器已成功集成到CMOST系统中。")
        print(f"现在可以在仿真中使用真实的生命表数据了。")
    else:
        print(f"\n❌ 演示过程中出现错误，请检查上述错误信息。")


if __name__ == "__main__":
    main()
