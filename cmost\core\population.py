"""
Population model for CMOST simulation.
"""
from typing import Dict, List, Optional, Tuple, Any, Callable
import numpy as np
import pandas as pd
from tqdm import tqdm
import logging

from ..models.patient import Patient
from ..config.settings import Settings


class PopulationGenerator:
    """Generator for creating realistic patient populations."""

    def __init__(self, settings: Settings):
        """Initialize population generator with settings.
        
        Args:
            settings: Simulation settings containing population parameters
        """
        self.settings = settings
        self.logger = logging.getLogger("CMOST_Population")

        # Set up logger if not already configured
        if not self.logger.handlers:
            self.logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def _get_setting(self, key: str, default=None):
        """Helper method to get settings with dot notation."""
        return self.settings.get(key, default)
    
    def generate_population(self, size: int) -> List[Patient]:
        """Generate a population of patients with realistic distributions.
        
        Args:
            size: Number of patients to generate
            
        Returns:
            List of generated patients
        """
        self.logger.info(f"Generating population of {size} patients")
        patients = []
        
        for i in tqdm(range(size), desc="Generating patients"):
            # Generate gender
            gender = self._generate_gender()
            
            # Generate age based on demographic distribution
            age = self._generate_age(gender)
            
            # Generate risk factors
            risk_factors = self._generate_risk_factors(gender, age)
            
            # Create patient
            patient = Patient(
                id=i,
                age=age,
                gender=gender,
                risk_factors=risk_factors
            )
            
            # Initialize polyps based on prevalence
            self._initialize_polyps(patient)

            # Initialize serrated lesions based on prevalence
            self._initialize_serrated_lesions(patient)

            # Set natural death year
            self._set_natural_death_year(patient)
            
            patients.append(patient)
        
        self.logger.info(f"Population generation complete: {len(patients)} patients")
        return patients
    
    def _generate_gender(self) -> str:
        """Generate patient gender based on population distribution.
        
        Returns:
            'M' for male or 'F' for female
        """
        return 'M' if np.random.random() < self._get_setting('ModelParameters.male_proportion', 0.5) else 'F'
    
    def _generate_age(self, gender: str) -> int:
        """Generate patient age based on demographic distribution.
        
        Args:
            gender: Patient gender ('M' or 'F')
            
        Returns:
            Age in years
        """
        age_distribution = self._get_setting('ModelParameters.age_distribution', None)
        if age_distribution:
            # Use configured age distribution if available
            age_dist = age_distribution.get(gender, age_distribution.get('default', None))
            if age_dist:
                return int(age_dist.sample())
        
        # Default implementation with realistic age distribution
        # Based on typical population pyramid
        age_brackets = [
            (0, 18, 0.20),   # 0-18 years: 20%
            (19, 35, 0.25),  # 19-35 years: 25%
            (36, 50, 0.20),  # 36-50 years: 20%
            (51, 65, 0.15),  # 51-65 years: 15%
            (66, 80, 0.15),  # 66-80 years: 15%
            (81, 100, 0.05)  # 81-100 years: 5%
        ]
        
        # Select age bracket
        bracket = self._sample_from_distribution(age_brackets)
        
        # Sample age within bracket
        min_age, max_age, _ = bracket
        return np.random.randint(min_age, max_age + 1)
    
    def _sample_from_distribution(self, distribution: List[Tuple]) -> Tuple:
        """Sample an item from a probability distribution.
        
        Args:
            distribution: List of tuples where the last element is the probability
            
        Returns:
            The sampled tuple
        """
        items = [item for item in distribution]
        probabilities = [item[-1] for item in distribution]
        
        # Normalize probabilities if they don't sum to 1
        total = sum(probabilities)
        if total != 1.0:
            probabilities = [p / total for p in probabilities]
            
        return items[np.random.choice(len(items), p=probabilities)]
    
    def _generate_risk_factors(self, gender: str, age: int) -> Dict[str, float]:
        """Generate risk factors based on patient demographics.
        
        Args:
            gender: Patient gender ('M' or 'F')
            age: Patient age in years
            
        Returns:
            Dictionary of risk factors and their values
        """
        risk_factors = {}
        
        # Family history (genetic risk)
        if hasattr(self.settings, 'family_history_prevalence'):
            has_family_history = np.random.random() < self.settings.family_history_prevalence
            risk_factors['family_history'] = self.settings.family_history_risk_multiplier if has_family_history else 1.0
        else:
            # Default values if not specified in settings
            has_family_history = np.random.random() < 0.10  # 10% prevalence
            risk_factors['family_history'] = 2.0 if has_family_history else 1.0
        
        # Smoking
        if hasattr(self.settings, 'smoking_prevalence'):
            smoking_prev = self.settings.smoking_prevalence.get(gender, 0.20)
        else:
            smoking_prev = 0.25 if gender == 'M' else 0.15  # Default values
            
        is_smoker = np.random.random() < smoking_prev
        risk_factors['smoking'] = 1.5 if is_smoker else 1.0
        
        # BMI-related risk
        # Generate BMI with realistic distribution
        if gender == 'M':
            bmi = np.random.normal(27.5, 4.5)  # Mean and SD for males
        else:
            bmi = np.random.normal(26.5, 5.0)  # Mean and SD for females
            
        bmi = max(16, min(45, bmi))  # Clamp to realistic range
        
        # Assign risk based on BMI
        if bmi < 18.5:  # Underweight
            risk_factors['bmi'] = 0.9
        elif bmi < 25:  # Normal weight
            risk_factors['bmi'] = 1.0
        elif bmi < 30:  # Overweight
            risk_factors['bmi'] = 1.2
        else:  # Obese
            risk_factors['bmi'] = 1.5
        
        # Physical activity
        activity_levels = [
            ('sedentary', 1.3, 0.30),
            ('light', 1.1, 0.40),
            ('moderate', 1.0, 0.20),
            ('high', 0.9, 0.10)
        ]
        
        # Adjust probabilities based on age
        if age > 65:
            # Older people more likely to be sedentary
            activity_levels = [
                ('sedentary', 1.3, 0.50),
                ('light', 1.1, 0.30),
                ('moderate', 1.0, 0.15),
                ('high', 0.9, 0.05)
            ]
        
        activity = self._sample_from_distribution(activity_levels)
        risk_factors['physical_activity'] = activity[1]
        
        # Diet-related risk
        diet_types = [
            ('poor', 1.4, 0.25),
            ('average', 1.0, 0.50),
            ('good', 0.8, 0.25)
        ]
        
        diet = self._sample_from_distribution(diet_types)
        risk_factors['diet'] = diet[1]
        
        # Alcohol consumption
        if gender == 'M':
            alcohol_levels = [
                ('none', 1.0, 0.20),
                ('light', 1.0, 0.40),
                ('moderate', 1.2, 0.30),
                ('heavy', 1.5, 0.10)
            ]
        else:
            alcohol_levels = [
                ('none', 1.0, 0.30),
                ('light', 1.0, 0.45),
                ('moderate', 1.2, 0.20),
                ('heavy', 1.5, 0.05)
            ]
            
        alcohol = self._sample_from_distribution(alcohol_levels)
        risk_factors['alcohol'] = alcohol[1]
        
        # Inflammatory bowel disease
        has_ibd = np.random.random() < 0.01  # ~1% prevalence
        risk_factors['ibd'] = 2.0 if has_ibd else 1.0
        
        return risk_factors
    
    def _initialize_polyps(self, patient: Patient) -> None:
        """Initialize patient with polyps based on prevalence.
        
        Args:
            patient: Patient to initialize with polyps
        """
        # Determine number of initial polyps based on age, gender, and risk
        age_key = min(patient.age // 10 * 10, max(self.settings.polyp_prevalence_by_age.keys()))
        base_prevalence = self.settings.polyp_prevalence_by_age[age_key]
        adjusted_prevalence = base_prevalence * patient.individual_risk
        
        # Poisson distribution for number of polyps
        num_polyps = np.random.poisson(adjusted_prevalence)
        
        for _ in range(num_polyps):
            location = self._sample_polyp_location()
            size = self._generate_initial_polyp_size()
            patient.add_polyp(location, size)
    
    def _sample_polyp_location(self) -> int:
        """Sample polyp location based on anatomical distribution.
        
        Returns:
            Location code (1-6 representing different colon segments)
        """
        if hasattr(self.settings, 'polyp_location_distribution'):
            return np.random.choice(
                range(1, 7),  # 6 colon segments
                p=self.settings.polyp_location_distribution
            )
        else:
            # Default distribution if not specified in settings
            # Based on typical anatomical distribution
            locations = [
                (1, 0.25),  # Rectum
                (2, 0.15),  # Sigmoid colon
                (3, 0.15),  # Descending colon
                (4, 0.15),  # Transverse colon
                (5, 0.15),  # Ascending colon
                (6, 0.15)   # Cecum
            ]
            location_ids = [loc[0] for loc in locations]
            probabilities = [loc[1] for loc in locations]
            return np.random.choice(location_ids, p=probabilities)
    
    def _generate_initial_polyp_size(self) -> float:
        """Generate initial polyp size with realistic distribution.
        
        Returns:
            Polyp size in cm
        """
        # Most polyps start small
        size_distribution = [
            (0.1, 0.3, 0.70),  # 0.1-0.3 cm: 70%
            (0.3, 0.5, 0.20),  # 0.3-0.5 cm: 20%
            (0.5, 1.0, 0.08),  # 0.5-1.0 cm: 8%
            (1.0, 2.0, 0.02)   # 1.0-2.0 cm: 2%
        ]
        
        min_size, max_size, _ = self._sample_from_distribution(size_distribution)
        return min_size + np.random.random() * (max_size - min_size)

    def _initialize_serrated_lesions(self, patient: Patient) -> None:
        """Initialize patient with serrated lesions based on prevalence.

        Args:
            patient: Patient to initialize with serrated lesions
        """
        # Serrated lesions are less common than adenomas
        # Prevalence increases with age and is slightly higher in females
        base_prevalence = 0.0

        if patient.age < 30:
            base_prevalence = 0.001
        elif patient.age < 50:
            base_prevalence = 0.01
        elif patient.age < 70:
            base_prevalence = 0.05
        else:
            base_prevalence = 0.15

        # Gender adjustment (slightly higher in females)
        gender_factor = 1.1 if patient.gender == 'F' else 0.9

        # Adjust for individual risk
        adjusted_prevalence = base_prevalence * gender_factor * patient.individual_risk

        # Poisson distribution for number of serrated lesions
        num_lesions = np.random.poisson(adjusted_prevalence)

        for _ in range(num_lesions):
            location = self._sample_serrated_lesion_location()
            size = self._generate_initial_serrated_lesion_size()
            patient.add_serrated_lesion(location, size)

    def _sample_serrated_lesion_location(self) -> int:
        """Sample serrated lesion location (more common in proximal colon).

        Returns:
            Location code (1-6 representing different colon segments)
        """
        # Serrated lesions are more common in proximal colon
        locations = [
            (1, 0.05),  # Rectum (rare)
            (2, 0.10),  # Sigmoid colon
            (3, 0.15),  # Descending colon
            (4, 0.25),  # Transverse colon
            (5, 0.30),  # Ascending colon (most common)
            (6, 0.15)   # Cecum
        ]
        location_ids = [loc[0] for loc in locations]
        probabilities = [loc[1] for loc in locations]
        return np.random.choice(location_ids, p=probabilities)

    def _generate_initial_serrated_lesion_size(self) -> float:
        """Generate initial serrated lesion size.

        Returns:
            Lesion size in cm
        """
        # Serrated lesions tend to be larger than adenomas at detection
        size_distribution = [
            (0.3, 0.6, 0.50),  # 0.3-0.6 cm: 50%
            (0.6, 1.0, 0.30),  # 0.6-1.0 cm: 30%
            (1.0, 2.0, 0.15),  # 1.0-2.0 cm: 15%
            (2.0, 3.0, 0.05)   # 2.0-3.0 cm: 5%
        ]

        min_size, max_size, _ = self._sample_from_distribution(size_distribution)
        return min_size + np.random.random() * (max_size - min_size)
    
    def _set_natural_death_year(self, patient: Patient) -> None:
        """Set the year when patient would die from natural causes.

        Args:
            patient: Patient to set natural death year for
        """
        current_age = patient.age

        if hasattr(self.settings, 'mortality_tables') and self.settings.mortality_tables:
            try:
                # Use the safer method from Settings class if available
                if hasattr(self.settings, 'get_mortality_rate'):
                    # Use the new integrated approach
                    death_age = current_age
                    max_age = 150  # Reasonable maximum age

                    while death_age <= max_age:
                        mortality_rate = self.settings.get_mortality_rate(death_age, patient.gender)
                        if np.random.random() < mortality_rate:
                            break
                        death_age += 1

                    # If we've reached maximum age, set death at max age
                    if death_age > max_age:
                        death_age = max_age

                    patient.set_natural_death_year(death_age - current_age)
                    return

                else:
                    # Legacy approach with error handling
                    mortality_table = self.settings.mortality_tables.get(patient.gender, None)
                    if mortality_table:
                        death_age = current_age

                        if isinstance(mortality_table, dict):
                            max_age = max(mortality_table.keys())
                            while death_age <= max_age:
                                mortality_rate = mortality_table.get(death_age, mortality_table.get(max_age, 0.1))
                                if np.random.random() < mortality_rate:
                                    break
                                death_age += 1
                        else:
                            # Handle legacy list format
                            while death_age < len(mortality_table) - 1:
                                mortality_rate = mortality_table[death_age]
                                if np.random.random() < mortality_rate:
                                    break
                                death_age += 1

                        patient.set_natural_death_year(death_age - current_age)
                        return

            except Exception as e:
                self.logger.warning(f"Error using mortality tables for patient {patient.id}: {e}")
                # Fall through to default calculation
        
        # Fallback to simplified life expectancy model if mortality tables not available
        if patient.gender == 'M':
            life_expectancy = 76
        else:
            life_expectancy = 81
            
        # Adjust for current age (older people tend to live longer than average remaining life)
        if current_age < 20:
            remaining_years = life_expectancy - current_age
        elif current_age < 40:
            remaining_years = life_expectancy - current_age + 1
        elif current_age < 60:
            remaining_years = life_expectancy - current_age + 2
        elif current_age < 80:
            remaining_years = life_expectancy - current_age + 3
        else:
            remaining_years = life_expectancy - current_age + 5
            
        # Add random variation
        remaining_years = max(1, int(np.random.normal(remaining_years, remaining_years * 0.2)))
        
        patient.set_natural_death_year(remaining_years)
    
    def stratify_population(self, patients: List[Patient], strata: Dict[str, Any]) -> Dict[str, List[Patient]]:
        """Stratify population into groups based on specified criteria.
        
        Args:
            patients: List of patients to stratify
            strata: Dictionary defining stratification criteria
            
        Returns:
            Dictionary mapping strata names to lists of patients
        """
        result = {}
        
        # Example stratification by age groups
        if 'age_groups' in strata:
            age_groups = strata['age_groups']
            for group_name, (min_age, max_age) in age_groups.items():
                result[group_name] = [p for p in patients if min_age <= p.age <= max_age]
        
        # Stratification by gender
        if 'gender' in strata:
            for gender in strata['gender']:
                result[gender] = [p for p in patients if p.gender == gender]
        
        # Stratification by risk level
        if 'risk_level' in strata:
            risk_levels = strata['risk_level']
            for level_name, (min_risk, max_risk) in risk_levels.items():
                result[level_name] = [p for p in patients if min_risk <= p.individual_risk <= max_risk]
        
        return result
    
    def get_population_statistics(self, patients: List[Patient]) -> Dict[str, Any]:
        """Calculate statistics for a population.
        
        Args:
            patients: List of patients
            
        Returns:
            Dictionary of population statistics
        """
        if not patients:
            return {}
            
        # Basic demographics
        total = len(patients)
        males = sum(1 for p in patients if p.gender == 'M')
        females = total - males
        
        # Age distribution
        ages = [p.age for p in patients]
        age_mean = np.mean(ages)
        age_median = np.median(ages)
        age_std = np.std(ages)
        
        # Risk factor distribution
        risk_scores = [p.individual_risk for p in patients]
        risk_mean = np.mean(risk_scores)
        risk_median = np.median(risk_scores)
        risk_std = np.std(risk_scores)
        
        # Polyp statistics
        polyp_counts = [len(p.polyps) for p in patients]
        patients_with_polyps = sum(1 for c in polyp_counts if c > 0)
        
        # Cancer statistics
        cancer_counts = [len(p.cancers) for p in patients]
        patients_with_cancer = sum(1 for c in cancer_counts if c > 0)
        
        return {
            'total_patients': total,
            'gender_distribution': {
                'male': males / total,
                'female': females / total
            },
            'age_statistics': {
                'mean': age_mean,
                'median': age_median,
                'std': age_std,
                'min': min(ages),
                'max': max(ages)
            },
            'risk_statistics': {
                'mean': risk_mean,
                'median': risk_median,
                'std': risk_std,
                'min': min(risk_scores),
                'max': max(risk_scores)
            },
            'disease_statistics': {
                'patients_with_polyps': patients_with_polyps,
                'patients_with_polyps_percent': patients_with_polyps / total * 100,
                'patients_with_cancer': patients_with_cancer,
                'patients_with_cancer_percent': patients_with_cancer / total * 100,
                'mean_polyps_per_patient': np.mean(polyp_counts),
                'mean_cancers_per_patient': np.mean(cancer_counts)
            }
        }
    
    def to_dataframe(self, patients: List[Patient]) -> pd.DataFrame:
        """Convert patient list to pandas DataFrame for analysis.
        
        Args:
            patients: List of patients
            
        Returns:
            DataFrame containing patient data
        """
        data = []
        for patient in patients:
            # Basic demographics
            patient_data = {
                'id': patient.id,
                'gender': patient.gender,
                'age': patient.age,
                'individual_risk': patient.individual_risk,
                'polyp_count': len(patient.polyps),
                'cancer_count': len(patient.cancers),
                'natural_death_year': patient.natural_death_year
            }
            
            # Add risk factors
            for factor, value in patient.risk_factors.items():
                patient_data[f'risk_{factor}'] = value
            
            data.append(patient_data)
            
        return pd.DataFrame(data)